import { v } from "convex/values";
import { internalMutation, internalQuery, query } from "./_generated/server";

export const getSubscription = internalQuery({
  args: { org: v.string() },
  handler: async (ctx, { org }) => {
    const subscription = await ctx.db
      .query("Subscription")
      .withIndex("by_organisation_id", (q) =>
        q.eq("organisation_id", org as any)
      )
      .first();

    return subscription;
  },
});

export const createSubscription = internalMutation({
  args: {
    org: v.string(),
    productId: v.string(),
    userId: v.string(),
    status: v.string(),
    subscription_id: v.string(),
  },
  handler: async (ctx, { org, productId, userId, status, subscription_id }) => {
    const subscription = await ctx.db.insert("Subscription", {
      organisation_id: org as any,
      product_id: productId,
      status,
      subscription_id,
      userId: userId as any,
    });

    return subscription;
  },
});

export const updateSubscription = internalMutation({
  args: {
    org: v.string(),
    productId: v.string(),
    userId: v.string(),
    status: v.string(),
    subscription_id: v.string(),
  },
  handler: async (ctx, { org, productId, userId, status, subscription_id }) => {
    const existingSubscription = await ctx.db
      .query("Subscription")
      .withIndex("by_organisation_id", (q) =>
        q.eq("organisation_id", org as any)
      )
      .first();

    if (!existingSubscription) {
      throw new Error("Subscription not found");
    }

    const subscription = await ctx.db.patch(existingSubscription._id, {
      product_id: productId,
      status,
      subscription_id,
      userId: userId as any,
    });

    return subscription;
  },
});

// Remove the old syncSubscription query since we're using HTTP endpoint now

export const performSubscriptionSync = internalMutation({
  args: {
    org: v.string(),
    productId: v.string(),
    userId: v.string(),
    status: v.string(),
    subscription_id: v.string(),
  },
  handler: async (ctx, { org, productId, userId, status, subscription_id }) => {
    // First, try to get existing subscription
    const existingSubscription = await ctx.db
      .query("Subscription")
      .withIndex("by_organisation_id", (q) =>
        q.eq("organisation_id", org as any)
      )
      .first();

    if (existingSubscription) {
      await ctx.db.patch(existingSubscription._id, {
        product_id: productId,
        status,
        subscription_id,
        userId: userId as any,
      });
    } else {
      await ctx.db.insert("Subscription", {
        organisation_id: org as any,
        product_id: productId,
        status,
        subscription_id,
        userId: userId as any,
      });
    }

    return {
      success: true,
    };
  },
});
