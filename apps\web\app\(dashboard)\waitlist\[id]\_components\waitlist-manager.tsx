"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Button } from "@workspace/ui/components/button";
import { ScrollArea } from "@workspace/ui/components/scroll-area";
import { ExternalLink, Users, Bar<PERSON>hart3, Loader2, Edit2 } from "lucide-react";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { toast } from "sonner";
import { InlineEditField } from "@workspace/ui/components/inline-field";
import { InlineEditTextArea } from "@workspace/ui/components/inline-textarea";
import WaitlistOverview from "./waitlist-overview";
import WaitlistAnalytics from "./waitlist-analytics";
import { Badge } from "@workspace/ui/components/badge";
import { Id } from "@workspace/backend/convex/_generated/dataModel";

interface WaitlistManagerProps {
  waitlistId: string;
}

export default function WaitlistManager({ waitlistId }: WaitlistManagerProps) {
  const [view, setView] = useState<"overview" | "analytics">("overview");

  // Fetch waitlist data using Convex
  const waitlist = useQuery(api.projects.waitlist.getWaitlist, { 
    id: waitlistId as Id<"Waitlist"> 
  });
  const updateWaitlistMutation = useMutation(api.projects.waitlist.updateWaitlist);

  const waitlistLoading = waitlist === undefined;

  const handleUpdateField = async (field: string, value: string) => {
    try {
      const updateData: any = { id: waitlistId as Id<"Waitlist"> };
      updateData[field] = value;
      await updateWaitlistMutation(updateData);
      toast.success(`${field} updated successfully`);
    } catch (error) {
      toast.error(`Failed to update ${field}`);
      console.error("Error updating field:", error);
    }
  };

  if (waitlistLoading || !waitlist) {
    return (
      <div className="container flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container ">
      {/* Header */}
      <div className="space-y-3 p-6">
        <div className="flex items-center flex-wrap gap-4 justify-between">
          <div className="space-y-1 grow flex-1 max-w-4xl">
            <div className="flex items-center gap-4">
              <Badge variant="outline">{waitlist.project?.name}</Badge>
              <InlineEditField
                value={waitlist.name || ""}
                onSave={(value) => handleUpdateField("name", value)}
                className="text-2xl md:text-3xl font-bold tracking-tight hover:bg-transparent focus:ring-2 focus:ring-offset-2 focus:ring-primary/20 rounded px-2 -ml-2"
              />
            </div>
            <p className="text-sm text-muted-foreground">
              {waitlist.description}
            </p>
          </div>
        </div>
      </div>

      {/* Custom Tabs */}
      <div className="w-full border-y">
        <ScrollArea className="w-full whitespace-nowrap">
          <div className="flex w-full gap-4 p-4">
            <button
              onClick={() => setView("overview")}
              className={cn(
                "inline-flex gap-3 items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
                view === "overview"
                  ? "bg-primary text-primary-foreground shadow-sm"
                  : "hover:bg-muted hover:text-muted-foreground"
              )}
            >
              <Users size={18} />
              Overview
            </button>
            <button
              onClick={() => setView("analytics")}
              className={cn(
                "inline-flex gap-3 items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
                view === "analytics"
                  ? "bg-primary text-primary-foreground shadow-sm"
                  : "hover:bg-muted hover:text-muted-foreground"
              )}
            >
              <BarChart3 size={18} />
              Analytics
            </button>
          </div>
        </ScrollArea>
      </div>

      {/* Content */}
      <div className="p-6">
        {view === "overview" ? (
          <WaitlistOverview waitlistId={waitlistId} />
        ) : null}

        {view === "analytics" ? (
          <WaitlistAnalytics waitlistId={waitlistId} />
        ) : null}
      </div>
    </div>
  );
}
