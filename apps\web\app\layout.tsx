import "@workspace/ui/globals.css";
import "@liveblocks/react-ui/styles.css";
import "@liveblocks/react-ui/styles/dark/attributes.css";
import "@liveblocks/react-blocknote/styles.css";
import type { Metadata } from "next";
import { Toaster } from "@workspace/ui/components/sonner";
import { ThemeProvider } from "@/context/theme-provider";
import { ConfirmDialogProvider } from "@workspace/ui/components/confirm-dialog";
import { GeistSans } from "geist/font/sans";
import { ConvexClientProvider } from "@/context/convex-provider";

export const metadata: Metadata = {
  title: "Ray AI - Build SaaS that users want and love.",
  description: "Design structured app flows with AI-generated PRDs",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${GeistSans.className} antialiased `}>
        <ConvexClientProvider>
          <ThemeProvider
            storageKey="rayai-theme"
            attribute="class"
            nonce="b1282rp=1ed2h3od12ndu2boqjdh1ibuo2i3hn"
            defaultTheme="light"
          >
            <ConfirmDialogProvider>
              {children}
              <Toaster richColors />
            </ConfirmDialogProvider>
          </ThemeProvider>
        </ConvexClientProvider>
      </body>
    </html>
  );
}
