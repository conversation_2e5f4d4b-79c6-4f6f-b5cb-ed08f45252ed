"use client";

import { useState, useEffect } from "react";
import { Mail } from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Switch } from "@workspace/ui/components/switch";
import { toast } from "sonner";
import { useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";

export type IntegrationConfig = {
  apiKey: string;
  listId?: string;
  audienceId?: string;
  [key: string]: any;
};

interface ResendIntegrationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  integration?: {
    id: string;
    name: string;
    config: IntegrationConfig;
    isActive: boolean;
  } | null;
  onSuccess: () => void;
}

export function ResendIntegrationModal({
  open,
  onOpenChange,
  integration,
  onSuccess,
}: ResendIntegrationModalProps) {
  // Use mutation state instead of manual loading
  const [formData, setFormData] = useState({
    name: "",
    apiKey: "",
    audienceId: "",
    isActive: true,
  });

  useEffect(() => {
    if (integration) {
      setFormData({
        name: integration.name,
        apiKey: integration.config.apiKey || "",
        audienceId: integration.config.audienceId || "",
        isActive: integration.isActive,
      });
    } else {
      setFormData({ name: "", apiKey: "", audienceId: "", isActive: true });
    }
  }, [integration, open]);

  // Convex mutations
  const createIntegrationMutation = useMutation(api.settings.integration.createIntegration);
  const updateIntegrationMutation = useMutation(api.settings.integration.updateIntegration);

  const handleSave = async () => {
    try {
      const config: IntegrationConfig = {
        apiKey: formData.apiKey,
      };
      if (formData.audienceId) config.audienceId = formData.audienceId;

      // Check if this is an existing integration (has a valid id)
      const isExistingIntegration =
        integration && integration.id && integration.id.trim() !== "";

      if (isExistingIntegration) {
        await updateIntegrationMutation({
          id: integration.id as any,
          name: formData.name,
          config,
          isActive: formData.isActive,
        });
      } else {
        await createIntegrationMutation({
          name: formData.name,
          type: "RESEND",
          config,
        });
      }

      toast.success("Integration saved successfully");
      onSuccess();
    } catch (error: any) {
      console.error("Integration save error:", error);
      toast.error(error.message || "Failed to save integration");
    }
  };

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Prevent multiple submissions
    if (isSubmitting) {
      return;
    }

    // Validate required fields
    if (!formData.name.trim()) {
      toast.error("Integration name is required");
      return;
    }

    if (!formData.apiKey.trim()) {
      toast.error("API key is required");
      return;
    }

    setIsSubmitting(true);
    await handleSave();
    setIsSubmitting(false);
  };

  const handleOpenChange = (newOpen: boolean) => {
    console.log("Modal open state changing:", {
      from: open,
      to: newOpen,
      isPending: isSubmitting,
    });
    if (!isSubmitting) {
      onOpenChange(newOpen);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            {integration && integration.id && integration.id.trim() !== ""
              ? "Edit Resend Integration"
              : "Add Resend Integration"}
          </DialogTitle>
          <DialogDescription>
            {integration && integration.id && integration.id.trim() !== ""
              ? "Update your Resend integration settings."
              : "Connect your Resend account to automatically sync waitlist subscribers."}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Integration Name</Label>
              <Input
                id="name"
                placeholder="e.g., My Resend Integration"
                value={formData.name}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setFormData({ ...formData, name: e.target.value })
                }
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="apiKey">API Key</Label>
              <Input
                id="apiKey"
                type="password"
                placeholder="re_123456789..."
                value={formData.apiKey}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setFormData({ ...formData, apiKey: e.target.value })
                }
                required
              />
              <p className="text-sm text-muted-foreground">
                Get your API key from the{" "}
                <a
                  href="https://resend.com/api-keys"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:underline"
                >
                  Resend dashboard
                </a>
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="audienceId">Audience ID (Optional)</Label>
              <Input
                id="audienceId"
                placeholder="12345678-1234-1234-1234-123456789012"
                value={formData.audienceId}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setFormData({ ...formData, audienceId: e.target.value })
                }
              />
              <p className="text-sm text-muted-foreground">
                Leave empty to use your default audience
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked: boolean) =>
                  setFormData({ ...formData, isActive: checked })
                }
              />
              <Label htmlFor="isActive">Active</Label>
            </div>
          </div>
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting
                ? integration && integration.id && integration.id.trim() !== ""
                  ? "Updating..."
                  : "Creating..."
                : integration && integration.id && integration.id.trim() !== ""
                  ? "Update Integration"
                  : "Create Integration"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
