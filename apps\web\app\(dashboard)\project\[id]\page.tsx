import { Badge } from "@workspace/ui/components/badge";
import { MilestoneMetrics } from "@/components/project/milestone-metrics";
import {
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Star,
  Bug,
  Target,
  Clock,
  User,
  Calendar,
  Activity,
} from "lucide-react";
import UpcomingDeadlines from "./_components/upcomingDeadlines";
import { ProjectPageClient } from "./_components/project-page-client";

interface ProjectPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function ProjectPage({ params }: ProjectPageProps) {
  const { id } = await params;

  return <ProjectPageClient projectId={id} />;
}
