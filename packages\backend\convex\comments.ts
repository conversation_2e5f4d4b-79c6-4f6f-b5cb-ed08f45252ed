import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { betterAuth } from "./auth";
import type { Id } from "./_generated/dataModel";

function now() {
  return Date.now();
}

// Threads
export const listThreads = query({
  args: {
    roomId: v.string(),
    cursor: v.optional(v.string()),
    numItems: v.optional(v.number()),
  },
  handler: async (ctx, { roomId, cursor, numItems }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Authentication required");
    const page = await ctx.db
      .query("CommentThread")
      .withIndex("by_room_lastActivity", (q) => q.eq("roomId", roomId))
      .order("desc")
      .paginate({ cursor: cursor ?? null, numItems: Math.min(numItems ?? 50, 100) });
    return page;
  },
});

export const createThread = mutation({
  args: {
    roomId: v.string(),
    text: v.string(),
    mentions: v.optional(v.array(v.string())),
    attachments: v.optional(
      v.array(
        v.object({ url: v.string(), name: v.string(), mime: v.string(), size: v.number() })
      )
    ),
    title: v.optional(v.string()),
  },
  handler: async (ctx, { roomId, text, mentions = [], attachments = [], title }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Authentication required");
    const organizationId = auth.organizationId;
    if (!organizationId) throw new Error("No organization");

    const threadId = await ctx.db.insert("CommentThread", {
      roomId,
      organizationId,
      title,
      createdBy: auth.id as Id<"User">,
      resolved: false,
      createdAt: now(),
      lastActivityAt: now(),
    });

    const commentId = await ctx.db.insert("Comment", {
      roomId,
      organizationId,
      threadId,
      authorId: auth.id as Id<"User">,
      text,
      mentions,
      createdAt: now(),
    });

    // attachments
    for (const a of attachments) {
      await ctx.db.insert("CommentAttachment", {
        roomId,
        organizationId,
        threadId,
        commentId,
        url: a.url,
        name: a.name,
        mime: a.mime,
        size: a.size,
        createdAt: now(),
      });
    }

    // auto-subscribe author at room level
    await ctx.db.insert("CommentSubscription", {
      roomId,
      organizationId,
      userId: auth.id as Id<"User">,
      createdAt: now(),
    });

    // notify mentions
    for (const userId of mentions) {
      if (userId === (auth.id as unknown as string)) continue;
      await ctx.db.insert("Notification", {
        userId: userId as unknown as Id<"User">,
        type: "mention",
        roomId,
        threadId,
        commentId,
        fromUserId: auth.id as Id<"User">,
        read: false,
        createdAt: now(),
        meta: undefined,
      });
    }

    return { threadId, commentId };
  },
});

export const resolveThread = mutation({
  args: { threadId: v.id("CommentThread"), resolved: v.boolean() },
  handler: async (ctx, { threadId, resolved }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Authentication required");
    const thread = await ctx.db.get(threadId);
    if (!thread) throw new Error("Thread not found");
    if (thread.organizationId !== auth.organizationId) throw new Error("Access denied");
    await ctx.db.patch(threadId, {
      resolved,
      resolvedBy: resolved ? (auth.id as Id<"User">) : undefined,
      lastActivityAt: now(),
    });
    return true;
  },
});

// Comments
export const listThreadComments = query({
  args: { threadId: v.id("CommentThread"), cursor: v.optional(v.string()), numItems: v.optional(v.number()) },
  handler: async (ctx, { threadId, cursor, numItems }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Authentication required");
    const thread = await ctx.db.get(threadId);
    if (!thread) throw new Error("Thread not found");
    if (thread.organizationId !== auth.organizationId) throw new Error("Access denied");
    return ctx.db
      .query("Comment")
      .withIndex("by_threadId", (q) => q.eq("threadId", threadId))
      .order("asc")
      .paginate({ cursor: cursor ?? null, numItems: Math.min(numItems ?? 50, 100) });
  },
});

export const reply = mutation({
  args: {
    threadId: v.id("CommentThread"),
    text: v.string(),
    mentions: v.optional(v.array(v.string())),
    attachments: v.optional(
      v.array(v.object({ url: v.string(), name: v.string(), mime: v.string(), size: v.number() }))
    ),
  },
  handler: async (ctx, { threadId, text, mentions = [], attachments = [] }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Authentication required");
    const thread = await ctx.db.get(threadId);
    if (!thread) throw new Error("Thread not found");
    if (thread.organizationId !== auth.organizationId) throw new Error("Access denied");

    const commentId = await ctx.db.insert("Comment", {
      roomId: thread.roomId,
      organizationId: thread.organizationId,
      threadId,
      authorId: auth.id as Id<"User">,
      text,
      mentions,
      createdAt: now(),
    });

    for (const a of attachments) {
      await ctx.db.insert("CommentAttachment", {
        roomId: thread.roomId,
        organizationId: thread.organizationId,
        threadId,
        commentId,
        url: a.url,
        name: a.name,
        mime: a.mime,
        size: a.size,
        createdAt: now(),
      });
    }

    await ctx.db.patch(threadId, { lastActivityAt: now() });

    // Notify mentions and subscribers except author
    const subs = await ctx.db
      .query("CommentSubscription")
      .withIndex("by_room_user", (q) => q.eq("roomId", thread.roomId))
      .collect();
    const recipientIds = new Set<string>();
    for (const s of subs) {
      if ((s.userId as unknown as string) !== (auth.id as unknown as string)) {
        recipientIds.add(s.userId as unknown as string);
      }
    }
    for (const userId of mentions) recipientIds.add(userId);

    for (const userId of recipientIds) {
      if (userId === (auth.id as unknown as string)) continue;
      await ctx.db.insert("Notification", {
        userId: userId as unknown as Id<"User">,
        type: "reply",
        roomId: thread.roomId,
        threadId,
        commentId,
        fromUserId: auth.id as Id<"User">,
        read: false,
        createdAt: now(),
        meta: undefined,
      });
    }
    return { commentId };
  },
});

export const edit = mutation({
  args: { commentId: v.id("Comment"), text: v.string() },
  handler: async (ctx, { commentId, text }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Authentication required");
    const comment = await ctx.db.get(commentId);
    if (!comment) throw new Error("Comment not found");
    if ((comment.authorId as unknown as string) !== (auth.id as unknown as string)) {
      throw new Error("Cannot edit");
    }
    await ctx.db.patch(commentId, { text, editedAt: now() });
    return true;
  },
});

export const del = mutation({
  args: { commentId: v.id("Comment") },
  handler: async (ctx, { commentId }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Authentication required");
    const comment = await ctx.db.get(commentId);
    if (!comment) throw new Error("Comment not found");
    if ((comment.authorId as unknown as string) !== (auth.id as unknown as string)) {
      throw new Error("Cannot delete");
    }
    await ctx.db.patch(commentId, { deletedAt: now(), text: "" });
    return true;
  },
});

// Reactions
export const react = mutation({
  args: { commentId: v.id("Comment"), emoji: v.string() },
  handler: async (ctx, { commentId, emoji }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Authentication required");
    const existing = await ctx.db
      .query("CommentReaction")
      .withIndex("by_comment_emoji", (q) => q.eq("commentId", commentId).eq("emoji", emoji))
      .filter((q) => q.eq(q.field("userId"), auth.id))
      .first();
    if (existing) {
      await ctx.db.delete(existing._id);
      return { toggledOff: true };
    }
    await ctx.db.insert("CommentReaction", {
      commentId,
      userId: auth.id as Id<"User">,
      emoji,
      createdAt: now(),
    });
    return { toggledOff: false };
  },
});

// Subscriptions
export const subscribe = mutation({
  args: { roomId: v.string(), threadId: v.optional(v.id("CommentThread")) },
  handler: async (ctx, { roomId, threadId }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Authentication required");
    const organizationId = auth.organizationId;
    const existing = await ctx.db
      .query("CommentSubscription")
      .withIndex("by_room_user", (q) => q.eq("roomId", roomId).eq("userId", auth.id))
      .first();
    if (existing) return true;
    await ctx.db.insert("CommentSubscription", {
      roomId,
      organizationId,
      threadId,
      userId: auth.id as Id<"User">,
      createdAt: now(),
    });
    return true;
  },
});

export const unsubscribe = mutation({
  args: { roomId: v.string(), threadId: v.optional(v.id("CommentThread")) },
  handler: async (ctx, { roomId, threadId }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Authentication required");
    const sub = await ctx.db
      .query("CommentSubscription")
      .withIndex("by_room_user", (q) => q.eq("roomId", roomId).eq("userId", auth.id))
      .first();
    if (sub) await ctx.db.delete(sub._id);
    return true;
  },
});

// Notifications
export const listNotifications = query({
  args: { cursor: v.optional(v.string()), numItems: v.optional(v.number()) },
  handler: async (ctx, { cursor, numItems }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Authentication required");
    return ctx.db
      .query("Notification")
      .withIndex("by_user_createdAt", (q) => q.eq("userId", auth.id))
      .order("desc")
      .paginate({ cursor: cursor ?? null, numItems: Math.min(numItems ?? 50, 100) });
  },
});

export const unreadCount = query({
  args: {},
  handler: async (ctx) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) return { count: 0 };
    const items = await ctx.db
      .query("Notification")
      .withIndex("by_user_read", (q) => q.eq("userId", auth.id).eq("read", false))
      .collect();
    return { count: items.length };
  },
});

export const markRead = mutation({
  args: { id: v.optional(v.id("Notification")) },
  handler: async (ctx, { id }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Authentication required");
    if (id) {
      const n = await ctx.db.get(id);
      if (!n || (n.userId as unknown as string) !== (auth.id as unknown as string)) return false;
      await ctx.db.patch(id, { read: true });
      return true;
    }
    const items = await ctx.db
      .query("Notification")
      .withIndex("by_user_read", (q) => q.eq("userId", auth.id).eq("read", false))
      .collect();
    for (const n of items) await ctx.db.patch(n._id, { read: true });
    return true;
  },
});


