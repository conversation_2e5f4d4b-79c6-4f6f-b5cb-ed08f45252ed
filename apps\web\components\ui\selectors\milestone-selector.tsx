"use client";

import { useState } from "react";
import { useQuery } from "convex/react";
import { api, Id } from "@workspace/backend";
import { Button } from "@workspace/ui/components/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@workspace/ui/components/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import { Badge } from "@workspace/ui/components/badge";
import { Check, ChevronsUpDown, Diamond, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { MilestoneStatusBadge } from "@/components/project/milestones/milestone-status-badge";

interface MilestoneSelectorProps {
  projectId: Id<"Project">;
  value?: string;
  onValueChange: (value: string | undefined) => void;
  placeholder?: string;
  className?: string;
}

export function MilestoneSelector({
  projectId,
  value,
  onValueChange,
  placeholder = "Select milestone",
  className,
}: MilestoneSelectorProps) {
  const [open, setOpen] = useState(false);

  const milestones = useQuery(api.projects.milestone.getMilestones, {
    id: projectId,
  });

  const selectedMilestone = (milestones ?? []).find(
    (milestone) => milestone._id === value
  );

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
            disabled={milestones === undefined}
          >
            {selectedMilestone ? (
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center gap-2">
                  <Diamond className="h-3 w-3 text-muted-foreground" />
                  <span className="truncate font-medium">
                    {selectedMilestone.name}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <MilestoneStatusBadge status={selectedMilestone.status} />
                  <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
                </div>
              </div>
            ) : (
              <div className="flex items-center gap-2 text-muted-foreground">
                <Diamond className="h-3 w-3" />
                <span>{placeholder}</span>
                <ChevronsUpDown className="ml-auto h-4 w-4 shrink-0 opacity-50" />
              </div>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0">
          <Command>
            <CommandInput placeholder="Search milestones..." />
            <CommandEmpty>
              {milestones === undefined ? "Loading..." : "No milestones found."}
            </CommandEmpty>
            <CommandGroup>
              {(milestones ?? []).map((milestone) => (
                <CommandItem
                  key={milestone._id}
                  value={milestone.name}
                  onSelect={() => {
                    onValueChange(milestone._id);
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === milestone._id ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <div className="flex flex-col w-full">
                    <div className="flex items-center gap-2">
                      <Diamond className="h-3 w-3" />
                      <span className="truncate">{milestone.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MilestoneStatusBadge status={milestone.status} />
                    </div>
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
