@import "tailwindcss";
@source "../../../apps/**/*.{ts,tsx}";
@source "../../../components/**/*.{ts,tsx}";
@source "../**/*.{ts,tsx}";

@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@utility container {
  @apply mx-auto max-w-[1800px] p-0;
}

:root {
  --background: oklch(0.9900 0 0);
  --foreground: oklch(0 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0 0 0);
  --popover: oklch(0.9900 0 0);
  --popover-foreground: oklch(0 0 0);
  --primary: oklch(0 0 0);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.9400 0 0);
  --secondary-foreground: oklch(0 0 0);
  --muted: oklch(0.9700 0 0);
  --muted-foreground: oklch(0.4400 0 0);
  --accent: oklch(0.9400 0 0);
  --accent-foreground: oklch(0 0 0);
  --destructive: oklch(0.6300 0.1900 23.0300);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.9200 0 0);
  --input: oklch(0.9400 0 0);
  --ring: oklch(0 0 0);
  --chart-1: oklch(0.7200 0.0800 142.0000);
  --chart-2: oklch(0.6800 0.0900 264.0000);
  --chart-3: oklch(0.7500 0.0700 25.0000);
  --chart-4: oklch(0.7000 0.1000 320.0000);
  --chart-5: oklch(0.7300 0.0850 200.0000);
  --chart-6: oklch(0.7800 0.0750 60.0000);
  --chart-7: oklch(0.6900 0.0950 285.0000);
  --chart-8: oklch(0.7400 0.0800 170.0000);
  --chart-9: oklch(0.7100 0.0850 15.0000);
  --chart-10: oklch(0.6600 0.1000 240.0000);
  --sidebar: oklch(0.9900 0 0);
  --sidebar-foreground: oklch(0 0 0);
  --sidebar-primary: oklch(0 0 0);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.9400 0 0);
  --sidebar-accent-foreground: oklch(0 0 0);
  --sidebar-border: oklch(0.9400 0 0);
  --sidebar-ring: oklch(0 0 0);
  --font-sans: Geist, sans-serif;
  --font-serif: Geist, sans-serif;
  --font-mono: Geist Mono, monospace;
  --radius: 0rem;
  --shadow-2xs: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.09);
  --shadow-xs: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.09);
  --shadow-sm: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.17), 0px 1px 2px -1px hsl(0 0% 0% / 0.17);
  --shadow: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.17), 0px 1px 2px -1px hsl(0 0% 0% / 0.17);
  --shadow-md: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.17), 0px 2px 4px -1px hsl(0 0% 0% / 0.17);
  --shadow-lg: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.17), 0px 4px 6px -1px hsl(0 0% 0% / 0.17);
  --shadow-xl: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.17), 0px 8px 10px -1px hsl(0 0% 0% / 0.17);
  --shadow-2xl: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.43);
  --tracking-normal: 0em;
  --spacing: 0.24rem;
}

.dark {
  --background: oklch(0 0 0);
  --foreground: oklch(1 0 0);
  --card: oklch(0.1400 0 0);
  --card-foreground: oklch(1 0 0);
  --popover: oklch(0.1800 0 0);
  --popover-foreground: oklch(1 0 0);
  --primary: oklch(1 0 0);
  --primary-foreground: oklch(0 0 0);
  --secondary: oklch(0.2500 0 0);
  --secondary-foreground: oklch(1 0 0);
  --muted: oklch(0.2300 0 0);
  --muted-foreground: oklch(0.7200 0 0);
  --accent: oklch(0.3200 0 0);
  --accent-foreground: oklch(1 0 0);
  --destructive: oklch(0.6900 0.2000 23.9100);
  --destructive-foreground: oklch(0 0 0);
  --border: oklch(0.2600 0 0);
  --input: oklch(0.3200 0 0);
  --ring: oklch(0.7200 0 0);
  --chart-1: oklch(0.6500 0.0800 142.0000);
  --chart-2: oklch(0.6100 0.0900 264.0000);
  --chart-3: oklch(0.6800 0.0700 25.0000);
  --chart-4: oklch(0.6300 0.1000 320.0000);
  --chart-5: oklch(0.6600 0.0850 200.0000);
  --chart-6: oklch(0.7100 0.0750 60.0000);
  --chart-7: oklch(0.6200 0.0950 285.0000);
  --chart-8: oklch(0.6700 0.0800 170.0000);
  --chart-9: oklch(0.6400 0.0850 15.0000);
  --chart-10: oklch(0.5900 0.1000 240.0000);
  --sidebar: oklch(0.1800 0 0);
  --sidebar-foreground: oklch(1 0 0);
  --sidebar-primary: oklch(1 0 0);
  --sidebar-primary-foreground: oklch(0 0 0);
  --sidebar-accent: oklch(0.3200 0 0);
  --sidebar-accent-foreground: oklch(1 0 0);
  --sidebar-border: oklch(0.3200 0 0);
  --sidebar-ring: oklch(0.7200 0 0);
  --font-sans: Geist, sans-serif;
  --font-serif: Geist, sans-serif;
  --font-mono: Geist Mono, monospace;
  --radius: 0rem;
  --shadow-2xs: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.09);
  --shadow-xs: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.09);
  --shadow-sm: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.17), 0px 1px 2px -1px hsl(0 0% 0% / 0.17);
  --shadow: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.17), 0px 1px 2px -1px hsl(0 0% 0% / 0.17);
  --shadow-md: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.17), 0px 2px 4px -1px hsl(0 0% 0% / 0.17);
  --shadow-lg: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.17), 0px 4px 6px -1px hsl(0 0% 0% / 0.17);
  --shadow-xl: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.17), 0px 8px 10px -1px hsl(0 0% 0% / 0.17);
  --shadow-2xl: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.43);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-chart-6: var(--chart-6);
  --color-chart-7: var(--chart-7);
  --color-chart-8: var(--chart-8);
  --color-chart-9: var(--chart-9);
  --color-chart-10: var(--chart-10);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

/* Base theme */
/* Start of Selection */
.bn-container[data-theming-css-variables-demo][data-color-scheme] {
  /* --bn-colors-editor-text: var(--foreground); */
  --bn-colors-editor-background: var(--background);
  /* --bn-colors-menu-text: var(--popover-foreground); */
  --bn-colors-menu-background: var(--popover);
  /* --bn-colors-tooltip-text: var(--popover-foreground); */
  /* --bn-colors-tooltip-background: var(--popover); */
  /* --bn-colors-hovered-text: var(--accent-foreground); */
  /* --bn-colors-hovered-background: var(--accent); */
  /* --bn-colors-selected-text: var(--accent-foreground); */
  /* --bn-colors-selected-background: var(--accent); */
  /* --bn-colors-disabled-text: var(--muted-foreground); */
  /* --bn-colors-disabled-background: var(--muted); */
  /* --bn-colors-shadow: var(--shadow); */
  /* --bn-colors-border: var(--border); */
  /* --bn-colors-side-menu: var(--muted-foreground); */
  --bn-color-highlight-colors: var(--accent);
  --bn-border-radius: 0px;
  --bn-font-family: var(--font-sans);
}

/* Changes for dark mode */
.bn-container[data-theming-css-variables-demo][data-color-scheme="dark"] {
  --bn-colors-editor-text: var(--foreground);
  --bn-colors-editor-background: var(--background);
  /* --bn-colors-side-menu: var(--muted-foreground); */
}

/* End of Selection */

* {
  @apply border-border;
  @apply transition-colors duration-200;
}

body {
  @apply bg-background text-foreground;
  font-size: 15px;
  scroll-behavior: smooth;
  font-weight: 400
}

html {
  @apply bg-background text-foreground;
  font-size: 15px;
  scroll-behavior: smooth;
  font-weight: 400
}

@layer components {
  .container {
    @apply mx-auto max-w-[1800px] p-0;
  }
}

/* Improved scroll behavior */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* Emoji picker adjustments */
em-emoji-picker {
  --rgb-background: hsl(var(--popover));
  --rgb-color: hsl(var(--popover-foreground));
  --rgb-accent: hsl(var(--accent));
  --rgb-input: hsl(var(--input));
  --rgb-border: hsl(var(--border));
}

/* Grid background pattern */
.bg-grid-white {
  background-image: 
    linear-gradient(to right, rgb(*********** / 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgb(*********** / 0.1) 1px, transparent 1px);
}

/* Syntax highlighting styles */
.hljs {
  background: transparent !important;
  padding: 0 !important;
}

/* Light theme syntax highlighting */
:root {
  --hljs-bg: hsl(0 0% 97%);
  --hljs-color: hsl(0 0% 20%);
  --hljs-keyword: hsl(220 100% 40%);
  --hljs-string: hsl(120 100% 25%);
  --hljs-comment: hsl(0 0% 45%);
  --hljs-number: hsl(30 100% 40%);
  --hljs-function: hsl(280 100% 40%);
  --hljs-variable: hsl(0 0% 20%);
  --hljs-operator: hsl(0 0% 20%);
  --hljs-punctuation: hsl(0 0% 20%);
  --hljs-property: hsl(200 100% 40%);
  --hljs-selector: hsl(150 100% 30%);
  --hljs-tag: hsl(220 100% 40%);
  --hljs-attr: hsl(200 100% 40%);
  --hljs-builtin: hsl(280 100% 40%);
  --hljs-literal: hsl(30 100% 40%);
  --hljs-title: hsl(220 100% 40%);
  --hljs-section: hsl(220 100% 40%);
  --hljs-addition: hsl(120 100% 25%);
  --hljs-deletion: hsl(0 100% 40%);
  --hljs-meta: hsl(0 0% 45%);
  --hljs-subst: hsl(0 0% 20%);
  --hljs-symbol: hsl(30 100% 40%);
  --hljs-bullet: hsl(30 100% 40%);
  --hljs-link: hsl(220 100% 40%);
  --hljs-emphasis: italic;
  --hljs-strong: bold;
}

/* Dark theme syntax highlighting */
.dark {
  --hljs-bg: hsl(0 0% 10%);
  --hljs-color: hsl(0 0% 90%);
  --hljs-keyword: hsl(220 100% 70%);
  --hljs-string: hsl(120 100% 70%);
  --hljs-comment: hsl(0 0% 60%);
  --hljs-number: hsl(30 100% 70%);
  --hljs-function: hsl(280 100% 70%);
  --hljs-variable: hsl(0 0% 90%);
  --hljs-operator: hsl(0 0% 90%);
  --hljs-punctuation: hsl(0 0% 90%);
  --hljs-property: hsl(200 100% 70%);
  --hljs-selector: hsl(150 100% 70%);
  --hljs-tag: hsl(220 100% 70%);
  --hljs-attr: hsl(200 100% 70%);
  --hljs-builtin: hsl(280 100% 70%);
  --hljs-literal: hsl(30 100% 70%);
  --hljs-title: hsl(220 100% 70%);
  --hljs-section: hsl(220 100% 70%);
  --hljs-addition: hsl(120 100% 70%);
  --hljs-deletion: hsl(0 100% 70%);
  --hljs-meta: hsl(0 0% 60%);
  --hljs-subst: hsl(0 0% 90%);
  --hljs-symbol: hsl(30 100% 70%);
  --hljs-bullet: hsl(30 100% 70%);
  --hljs-link: hsl(220 100% 70%);
  --hljs-emphasis: italic;
  --hljs-strong: bold;
}

/* Apply syntax highlighting colors */
.hljs-keyword,
.hljs-selector-tag,
.hljs-literal,
.hljs-section,
.hljs-link {
  color: var(--hljs-keyword) !important;
}

.hljs-string,
.hljs-title,
.hljs-name,
.hljs-type,
.hljs-attribute,
.hljs-symbol,
.hljs-bullet,
.hljs-addition,
.hljs-variable,
.hljs-template-tag,
.hljs-template-variable {
  color: var(--hljs-string) !important;
}

.hljs-comment,
.hljs-quote,
.hljs-deletion,
.hljs-meta {
  color: var(--hljs-comment) !important;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-literal,
.hljs-title,
.hljs-section,
.hljs-doctag,
.hljs-type,
.hljs-name,
.hljs-strong {
  font-weight: bold !important;
}

.hljs-emphasis {
  font-style: italic !important;
}

/* Code block styling */
pre code {
  background: var(--hljs-bg) !important;
  color: var(--hljs-color) !important;
  border-radius: 0.5rem;
  padding: 1rem;
  overflow-x: auto;
  font-family: var(--font-mono);
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Inline code styling */
:not(pre) > code {
  background: var(--muted) !important;
  color: var(--foreground) !important;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: var(--font-mono);
  font-size: 0.875em;
}
