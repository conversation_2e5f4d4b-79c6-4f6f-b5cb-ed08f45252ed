import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { betterAuth } from "../auth";
import { IntegrationType } from "../../schema/enum";

// Get all integrations for the organization
export const getAllIntegrations = query({
  args: {},
  handler: async (ctx) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }

    const integrations = await ctx.db
      .query("Integration")
      .filter((q) => q.eq(q.field("organizationId"), identity.organizationId))
      .collect();

    return integrations;
  },
});

// Create a new integration
export const createIntegration = mutation({
  args: {
    name: v.string(),
    type: IntegrationType,
    config: v.any(), // JSON configuration
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }

    const integrationId = await ctx.db.insert("Integration", {
      name: args.name,
      type: args.type,
      config: args.config,
      isActive: true,
      organizationId: identity.organizationId,
      createdById: identity.id,
    });

    return { success: true, id: integrationId };
  },
});

// Update an integration
export const updateIntegration = mutation({
  args: {
    id: v.id("Integration"),
    name: v.optional(v.string()),
    config: v.optional(v.any()),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }

    // Verify the integration belongs to the user's organization
    const integration = await ctx.db.get(args.id);
    if (!integration || integration.organizationId !== identity.organizationId) {
      throw new Error("Integration not found or unauthorized");
    }

    const { id, ...updates } = args;
    await ctx.db.patch(id, updates);

    return { success: true };
  },
});

// Delete an integration
export const deleteIntegration = mutation({
  args: {
    id: v.id("Integration"),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }

    // Verify the integration belongs to the user's organization
    const integration = await ctx.db.get(args.id);
    if (!integration || integration.organizationId !== identity.organizationId) {
      throw new Error("Integration not found or unauthorized");
    }

    // Delete all usage records for this integration
    const usageRecords = await ctx.db
      .query("IntegrationUsage")
      .filter((q) => q.eq(q.field("integrationId"), args.id))
      .collect();

    for (const usage of usageRecords) {
      await ctx.db.delete(usage._id);
    }

    // Delete the integration
    await ctx.db.delete(args.id);

    return { success: true };
  },
});

// Get integrations for a specific purpose
export const getIntegrationsForPurpose = query({
  args: {
    purpose: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }

    const integrations = await ctx.db
      .query("Integration")
      .filter((q) => q.eq(q.field("organizationId"), identity.organizationId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    // Filter integrations based on purpose compatibility
    // This is a simplified version - you might want to add more sophisticated filtering
    return integrations.filter((integration) => {
      switch (args.purpose) {
        case "email_sync":
          return ["RESEND", "LOOPS", "SENDGRID", "MAILCHIMP", "CONVERTKIT"].includes(integration.type);
        case "webhook":
          return ["GITHUB"].includes(integration.type);
        default:
          return true;
      }
    });
  },
});
