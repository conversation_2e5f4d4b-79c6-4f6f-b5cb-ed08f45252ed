"use client";

import { useState, useMemo } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Badge } from "@workspace/ui/components/badge";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { Card } from "@workspace/ui/components/card";
import {
  Search,
  MoreHorizontal,
  <PERSON>,
  Trash2,
  <PERSON>load,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>hare2,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Target,
  Loader2,
} from "lucide-react";
import { toast } from "sonner";
import { format } from "date-fns";
import { useDebounce } from "@/hooks/use-debounce";

interface WaitlistOverviewProps {
  waitlistId: string;
}

export default function WaitlistOverview({
  waitlistId,
}: WaitlistOverviewProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedEntries, setSelectedEntries] = useState<string[]>([]);
  const [statusFilter, setStatusFilter] = useState<string>("all");

  // Debounce search query
  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  // Check if we have active filters
  const hasActiveFilters = useMemo(() => {
    return Boolean(
      (debouncedSearchQuery && debouncedSearchQuery.trim() !== "") ||
        (statusFilter && statusFilter !== "all")
    );
  }, [debouncedSearchQuery, statusFilter]);

  // Use Convex hooks for real-time data
  const entriesData = useQuery(api.projects.waitlist.getWaitlistEntries, {
    waitlistId: waitlistId as Id<"Waitlist">,
    search: hasActiveFilters && debouncedSearchQuery ? debouncedSearchQuery : undefined,
    status: hasActiveFilters && statusFilter !== "all" ? statusFilter : undefined,
    limit: 100,
    offset: 0,
  });

  const updateEntryStatusMutation = useMutation(api.projects.waitlist.updateEntryStatus);
  const deleteEntryMutation = useMutation(api.projects.waitlist.deleteEntry);

  const isLoading = entriesData === undefined;
  const entries = entriesData?.entries || [];



  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin" />
      </div>
    );
  }

  const handleStatusChange = async (entryId: string, newStatus: string) => {
    try {
      await updateEntryStatusMutation({
        entryId: entryId as Id<"WaitlistEntry">,
        status: newStatus,
      });
      toast.success("Status updated successfully");
    } catch (error) {
      toast.error("Failed to update status");
    }
  };

  const handleDeleteEntry = async (entryId: string) => {
    try {
      await deleteEntryMutation({
        entryId: entryId as Id<"WaitlistEntry">,
      });
      toast.success("Entry deleted successfully");
    } catch (error) {
      toast.error("Failed to delete entry");
    }
  };

  const handleBulkInvite = async () => {
    try {
      await Promise.all(
        selectedEntries.map((entryId) =>
          updateEntryStatusMutation({
            entryId: entryId as Id<"WaitlistEntry">,
            status: "invited",
          })
        )
      );
      toast.success(`Invited ${selectedEntries.length} users`);
      setSelectedEntries([]);
    } catch (error) {
      toast.error("Failed to send invites");
    }
  };

  const handleExportCSV = () => {
    const csvData = [
      [
        "Email",
        "Name",
        "Status",
        "Position",
        "Referrals",
        "Joined Date",
        "UTM Source",
        "UTM Medium",
        "UTM Campaign",
      ],
      ...entries.map((entry) => [
        entry.email,
        entry.name || "",
        entry.status,
        entry.position.toString(),
        entry.referrals.length.toString(),
        entry.joinedAt || "",
        entry.utmSource || "",
        entry.utmMedium || "",
        entry.utmCampaign || "",
      ]),
    ];

    const csvContent = csvData.map((row) => row.join(",")).join("\n");
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `waitlist-entries-${new Date().toISOString().split("T")[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return <Badge variant="secondary">Pending</Badge>;
      case "verified":
        return <Badge variant="default">Verified</Badge>;
      case "invited":
        return <Badge variant="outline">Invited</Badge>;
      case "joined":
        return (
          <Badge className="bg-green-500 hover:bg-green-600">Joined</Badge>
        );
      case "bounced":
        return <Badge variant="destructive">Bounced</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center flex-1">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search entries..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <div className="flex items-center gap-2">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="verified">Verified</SelectItem>
                <SelectItem value="invited">Invited</SelectItem>
                <SelectItem value="joined">Joined</SelectItem>
                <SelectItem value="bounced">Bounced</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex gap-2">
          {selectedEntries.length > 0 && (
            <Button
              onClick={handleBulkInvite}
              size="sm"
              variant="outline"
            >
              <Mail className="h-4 w-4 mr-2" />
              Invite Selected ({selectedEntries.length})
            </Button>
          )}
          <Button onClick={handleExportCSV} size="sm" variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
        </div>
      </div>

      {/* Entries Table */}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={
                  entries.length > 0 &&
                  selectedEntries.length === entries.length
                }
                onCheckedChange={(checked) => {
                  if (checked) {
                    setSelectedEntries(entries.map((e) => e.id));
                  } else {
                    setSelectedEntries([]);
                  }
                }}
              />
            </TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Position</TableHead>
            <TableHead>Referrals</TableHead>
            <TableHead>Joined</TableHead>
            <TableHead>Source</TableHead>
            <TableHead className="w-12"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {entries.map((entry) => (
            <TableRow key={entry.id}>
              <TableCell>
                <Checkbox
                  checked={selectedEntries.includes(entry.id)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedEntries([...selectedEntries, entry.id]);
                    } else {
                      setSelectedEntries(
                        selectedEntries.filter((id) => id !== entry.id)
                      );
                    }
                  }}
                />
              </TableCell>
              <TableCell className="font-medium">{entry.email}</TableCell>
              <TableCell>{entry.name || "-"}</TableCell>
              <TableCell>{getStatusBadge(entry.status)}</TableCell>
              <TableCell>#{entry.position}</TableCell>
              <TableCell>
                <div className="flex items-center gap-1">
                  <Share2 className="h-3 w-3" />
                  {entry.referrals.length}
                </div>
              </TableCell>
              <TableCell>
                {entry.joinedAt
                  ? format(new Date(entry.joinedAt), "MMM d, yyyy")
                  : "-"}
              </TableCell>
              <TableCell>
                <span className="text-xs text-muted-foreground capitalize">
                  {entry.utmSource || "direct"}
                </span>
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {entry.status === "VERIFIED" && (
                      <DropdownMenuItem
                        onClick={() => handleStatusChange(entry.id, "INVITED")}
                      >
                        <Mail className="h-4 w-4 mr-2" />
                        Send Invite
                      </DropdownMenuItem>
                    )}
                    {entry.status === "PENDING" && (
                      <DropdownMenuItem
                        onClick={() => handleStatusChange(entry.id, "VERIFIED")}
                      >
                        <UserCheck className="h-4 w-4 mr-2" />
                        Mark Verified
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem
                      onClick={() => handleDeleteEntry(entry.id)}
                      className="text-destructive"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* No results message */}
      {!isLoading && entries.length === 0 && (
        <div className="text-center py-8">
          <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-muted-foreground mb-2">
            No entries found
          </h3>
          <p className="text-sm text-muted-foreground">
            {searchQuery || statusFilter !== "all"
              ? "Try adjusting your search or filter criteria"
              : "No entries have been added to this waitlist yet"}
          </p>
        </div>
      )}
    </div>
  );
}
