import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";

export function Badge({ id }: { id: string }) {
  const project = useQuery(api.projects.index.getProject, {
    id: id as Id<"Project">,
  });

  return (
    <>
      {project ? (
        <div className="bg-primary/70 flex-col gap-2 flex items-center text-primary-foreground mx-2 p-2 rounded-lg">
          <h6>{project?.name}</h6>
        </div>
      ) : null}
    </>
  );
}
