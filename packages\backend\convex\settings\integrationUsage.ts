import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { betterAuth } from "../auth";

// Create integration usage (link integration to entity)
export const createIntegrationUsage = mutation({
  args: {
    integrationId: v.id("Integration"),
    entityType: v.string(),
    entityId: v.string(),
    purpose: v.string(),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }

    // Verify the integration belongs to the user's organization
    const integration = await ctx.db.get(args.integrationId);
    if (!integration || integration.organizationId !== identity.organizationId) {
      throw new Error("Integration not found or unauthorized");
    }

    // Check if usage already exists
    const existingUsage = await ctx.db
      .query("IntegrationUsage")
      .filter((q) => q.eq(q.field("integrationId"), args.integrationId))
      .filter((q) => q.eq(q.field("entityType"), args.entityType))
      .filter((q) => q.eq(q.field("entityId"), args.entityId))
      .filter((q) => q.eq(q.field("purpose"), args.purpose))
      .unique();

    if (existingUsage) {
      // Update existing usage
      await ctx.db.patch(existingUsage._id, {
        isActive: args.isActive ?? true,
      });
      return { success: true, id: existingUsage._id };
    } else {
      // Create new usage
      const usageId = await ctx.db.insert("IntegrationUsage", {
        integrationId: args.integrationId,
        entityType: args.entityType,
        entityId: args.entityId,
        purpose: args.purpose,
        isActive: args.isActive ?? true,
      });
      return { success: true, id: usageId };
    }
  },
});

// Get integration usage for an entity and purpose
export const getIntegrationUsage = query({
  args: {
    entityType: v.string(),
    entityId: v.string(),
    purpose: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }

    const usage = await ctx.db
      .query("IntegrationUsage")
      .filter((q) => q.eq(q.field("entityType"), args.entityType))
      .filter((q) => q.eq(q.field("entityId"), args.entityId))
      .filter((q) => q.eq(q.field("purpose"), args.purpose))
      .unique();

    if (!usage) {
      return null;
    }

    // Get the integration details
    const integration = await ctx.db.get(usage.integrationId);
    if (!integration || integration.organizationId !== identity.organizationId) {
      return null;
    }

    return {
      ...usage,
      integration,
    };
  },
});

// Update integration usage
export const updateIntegrationUsage = mutation({
  args: {
    id: v.id("IntegrationUsage"),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }

    // Get the usage record
    const usage = await ctx.db.get(args.id);
    if (!usage) {
      throw new Error("Usage record not found");
    }

    // Verify the integration belongs to the user's organization
    const integration = await ctx.db.get(usage.integrationId);
    if (!integration || integration.organizationId !== identity.organizationId) {
      throw new Error("Unauthorized");
    }

    const { id, ...updates } = args;
    await ctx.db.patch(id, updates);

    return { success: true };
  },
});

// Delete integration usage
export const deleteIntegrationUsage = mutation({
  args: {
    id: v.id("IntegrationUsage"),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }

    // Get the usage record
    const usage = await ctx.db.get(args.id);
    if (!usage) {
      throw new Error("Usage record not found");
    }

    // Verify the integration belongs to the user's organization
    const integration = await ctx.db.get(usage.integrationId);
    if (!integration || integration.organizationId !== identity.organizationId) {
      throw new Error("Unauthorized");
    }

    await ctx.db.delete(args.id);

    return { success: true };
  },
});

// Get all integration usages for an entity
export const getIntegrationUsagesForEntity = query({
  args: {
    entityType: v.string(),
    entityId: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }

    const usages = await ctx.db
      .query("IntegrationUsage")
      .filter((q) => q.eq(q.field("entityType"), args.entityType))
      .filter((q) => q.eq(q.field("entityId"), args.entityId))
      .collect();

    // Get integration details for each usage and filter by organization
    const usagesWithIntegrations = [];
    for (const usage of usages) {
      const integration = await ctx.db.get(usage.integrationId);
      if (integration && integration.organizationId === identity.organizationId) {
        usagesWithIntegrations.push({
          ...usage,
          integration,
        });
      }
    }

    return usagesWithIntegrations;
  },
});
