import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { betterAuth } from "../auth";
import { AssetType, AssetCategory, LinkType } from "../../schema/enum";
import { ConvexError } from "convex/values";

// QUERIES

// Get all assets for a project with optional filtering
export const getProjectAssets = query({
  args: {
    projectId: v.id("Project"),
    type: v.optional(AssetType),
    category: v.optional(AssetCategory),
    search: v.optional(v.string()),
  },
  handler: async (ctx, { projectId, type, category, search }) => {
    const session = await betterAuth.getAuthUser(ctx);
    if (!session) {
      throw new ConvexError("Authentication required");
    }

    // Verify project belongs to user's organization
    const project = await ctx.db.get(projectId);
    if (!project || project.organizationId !== session.organizationId) {
      throw new ConvexError("Project not found or access denied");
    }

    let query = ctx.db
      .query("Asset")
      .withIndex("by_project", (q) => q.eq("projectId", projectId));

    const assets = await query.collect();

    // Apply filters
    let filteredAssets = assets;

    if (type) {
      filteredAssets = filteredAssets.filter((asset) => asset.type === type);
    }

    if (category) {
      filteredAssets = filteredAssets.filter((asset) => asset.category === category);
    }

    if (search) {
      const searchLower = search.toLowerCase();
      filteredAssets = filteredAssets.filter(
        (asset) =>
          asset.name.toLowerCase().includes(searchLower) ||
          (asset.description && asset.description.toLowerCase().includes(searchLower)) ||
          asset.tags.some((tag) => tag.toLowerCase().includes(searchLower))
      );
    }

    // Sort by creation time (newest first)
    filteredAssets.sort((a, b) => b._creationTime - a._creationTime);

    return filteredAssets;
  },
});

// Get a single asset by ID
export const getAsset = query({
  args: { id: v.id("Asset") },
  handler: async (ctx, { id }) => {
    const session = await betterAuth.getAuthUser(ctx);
    if (!session) {
      throw new ConvexError("Authentication required");
    }

    const asset = await ctx.db.get(id);
    if (!asset || asset.organizationId !== session.organizationId) {
      throw new ConvexError("Asset not found or access denied");
    }

    return asset;
  },
});

// Get asset analytics (views, downloads)
export const getAssetAnalytics = query({
  args: { assetId: v.id("Asset") },
  handler: async (ctx, { assetId }) => {
    const session = await betterAuth.getAuthUser(ctx);
    if (!session) {
      throw new ConvexError("Authentication required");
    }

    // Verify asset belongs to user's organization
    const asset = await ctx.db.get(assetId);
    if (!asset || asset.organizationId !== session.organizationId) {
      throw new ConvexError("Asset not found or access denied");
    }

    // Get views and downloads
    const views = await ctx.db
      .query("AssetView")
      .filter((q) => q.eq(q.field("assetId"), assetId))
      .collect();

    const downloads = await ctx.db
      .query("AssetDownload")
      .filter((q) => q.eq(q.field("assetId"), assetId))
      .collect();

    return {
      totalViews: views.length,
      totalDownloads: downloads.length,
      recentViews: views.slice(-10).reverse(),
      recentDownloads: downloads.slice(-10).reverse(),
    };
  },
});

// MUTATIONS

// Create a new file asset
export const createFileAsset = mutation({
  args: {
    projectId: v.id("Project"),
    name: v.string(),
    description: v.optional(v.string()),
    url: v.string(),
    fileName: v.string(),
    fileSize: v.number(),
    mimeType: v.string(),
    type: AssetType,
    category: v.optional(AssetCategory),
    tags: v.optional(v.array(v.string())),
    isPublic: v.optional(v.boolean()),
  },
  handler: async (
    ctx,
    {
      projectId,
      name,
      description,
      url,
      fileName,
      fileSize,
      mimeType,
      type,
      category,
      tags,
      isPublic,
    }
  ) => {
    const session = await betterAuth.getAuthUser(ctx);
    if (!session) {
      throw new ConvexError("Authentication required");
    }

    // Verify project belongs to user's organization
    const project = await ctx.db.get(projectId);
    if (!project || project.organizationId !== session.organizationId) {
      throw new ConvexError("Project not found or access denied");
    }

    const assetId = await ctx.db.insert("Asset", {
      name,
      description,
      type,
      projectId,
      organizationId: session.organizationId,
      url,
      fileName,
      fileSize,
      mimeType,
      category,
      tags: tags || [],
      isPublic: isPublic || false,
      uploadedById: session.id,
    });

    return assetId;
  },
});

// Create a new link asset
export const createLinkAsset = mutation({
  args: {
    projectId: v.id("Project"),
    name: v.string(),
    description: v.optional(v.string()),
    url: v.string(),
    linkType: LinkType,
    category: v.optional(AssetCategory),
    tags: v.optional(v.array(v.string())),
    isPublic: v.optional(v.boolean()),
  },
  handler: async (
    ctx,
    { projectId, name, description, url, linkType, category, tags, isPublic }
  ) => {
    const session = await betterAuth.getAuthUser(ctx);
    if (!session) {
      throw new ConvexError("Authentication required");
    }

    // Verify project belongs to user's organization
    const project = await ctx.db.get(projectId);
    if (!project || project.organizationId !== session.organizationId) {
      throw new ConvexError("Project not found or access denied");
    }

    const assetId = await ctx.db.insert("Asset", {
      name,
      description,
      type: "link",
      projectId,
      organizationId: session.organizationId,
      url,
      linkType,
      category,
      tags: tags || [],
      isPublic: isPublic || false,
      uploadedById: session.id,
    });

    return assetId;
  },
});

// Update an asset
export const updateAsset = mutation({
  args: {
    id: v.id("Asset"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    category: v.optional(AssetCategory),
    tags: v.optional(v.array(v.string())),
    isPublic: v.optional(v.boolean()),
  },
  handler: async (ctx, { id, name, description, category, tags, isPublic }) => {
    const session = await betterAuth.getAuthUser(ctx);
    if (!session) {
      throw new ConvexError("Authentication required");
    }

    // Verify asset belongs to user's organization
    const asset = await ctx.db.get(id);
    if (!asset || asset.organizationId !== session.organizationId) {
      throw new ConvexError("Asset not found or access denied");
    }

    const updates: any = {};
    if (name !== undefined) updates.name = name;
    if (description !== undefined) updates.description = description;
    if (category !== undefined) updates.category = category;
    if (tags !== undefined) updates.tags = tags;
    if (isPublic !== undefined) updates.isPublic = isPublic;

    await ctx.db.patch(id, updates);
    return true;
  },
});

// Delete an asset
export const deleteAsset = mutation({
  args: { id: v.id("Asset") },
  handler: async (ctx, { id }) => {
    const session = await betterAuth.getAuthUser(ctx);
    if (!session) {
      throw new ConvexError("Authentication required");
    }

    // Verify asset belongs to user's organization
    const asset = await ctx.db.get(id);
    if (!asset || asset.organizationId !== session.organizationId) {
      throw new ConvexError("Asset not found or access denied");
    }

    // Delete related views and downloads
    const views = await ctx.db
      .query("AssetView")
      .filter((q) => q.eq(q.field("assetId"), id))
      .collect();

    const downloads = await ctx.db
      .query("AssetDownload")
      .filter((q) => q.eq(q.field("assetId"), id))
      .collect();

    // Delete all related records
    for (const view of views) {
      await ctx.db.delete(view._id);
    }

    for (const download of downloads) {
      await ctx.db.delete(download._id);
    }

    // Delete the asset
    await ctx.db.delete(id);
    return true;
  },
});

// Record asset view
export const recordAssetView = mutation({
  args: {
    assetId: v.id("Asset"),
    ipAddress: v.string(),
    userAgent: v.optional(v.string()),
    referrer: v.optional(v.string()),
  },
  handler: async (ctx, { assetId, ipAddress, userAgent, referrer }) => {
    const session = await betterAuth.getAuthUser(ctx);

    // Verify asset exists and is accessible
    const asset = await ctx.db.get(assetId);
    if (!asset) {
      throw new ConvexError("Asset not found");
    }

    // If asset is not public, require authentication and organization access
    if (!asset.isPublic) {
      if (!session || asset.organizationId !== session.organizationId) {
        throw new ConvexError("Asset not found or access denied");
      }
    }

    await ctx.db.insert("AssetView", {
      assetId,
      organizationId: asset.organizationId,
      userId: session?.id,
      ipAddress,
      userAgent,
      referrer,
      viewedAt: Date.now(),
    });

    return true;
  },
});

// Record asset download
export const recordAssetDownload = mutation({
  args: {
    assetId: v.id("Asset"),
    ipAddress: v.string(),
    userAgent: v.optional(v.string()),
  },
  handler: async (ctx, { assetId, ipAddress, userAgent }) => {
    const session = await betterAuth.getAuthUser(ctx);

    // Verify asset exists and is accessible
    const asset = await ctx.db.get(assetId);
    if (!asset) {
      throw new ConvexError("Asset not found");
    }

    // If asset is not public, require authentication and organization access
    if (!asset.isPublic) {
      if (!session || asset.organizationId !== session.organizationId) {
        throw new ConvexError("Asset not found or access denied");
      }
    }

    await ctx.db.insert("AssetDownload", {
      assetId,
      organizationId: asset.organizationId,
      userId: session?.id,
      ipAddress,
      userAgent,
      downloadedAt: Date.now(),
    });

    return true;
  },
});
