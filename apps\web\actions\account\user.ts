"use server";
import { redirect } from "next/navigation";
import { api } from "@workspace/backend";
import { fetchQuery } from "convex/nextjs";

export async function getSession() {
  const data = await fetchQuery(api.auth.getFullSession);

  const { org, session, user } = data;

  if (!session || !user) {
    redirect("/auth/sign-in");
  }

  if (!org?.id || !org.name) {
    redirect("/switch-org");
  }

  const me = org.members.find((m) => m.userId === session.userId);

  if (!me) {
    redirect("/switch-org");
  }

  return {
    userId: user.id,
    org: org.id,
    email: user.email,
    name: user.name,
    image: user.image,
    role: me.role,
    orgName: org.name,
    memberId: me.id,
    token: session.token,
  };
}
