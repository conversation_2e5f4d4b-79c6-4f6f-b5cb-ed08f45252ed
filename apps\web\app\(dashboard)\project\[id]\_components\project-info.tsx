"use client";

import { InlineEditField } from "@workspace/ui/components/inline-field";
import { toast } from "sonner";
import { InlineEditTextArea } from "@workspace/ui/components/inline-textarea";
import { Button } from "@workspace/ui/components/button";
import Link from "next/link";
import { ProjectTypeSelector } from "@/components/ui/selectors/project-type-selector";
import { ProjectPlatform } from "@workspace/backend/prisma/generated/client/client";
import { useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";

export const ProjectInfo = ({
  title,
  description,
  id,
  platform,
  token,
}: {
  title: string;
  description: string;
  id: string;
  platform: ProjectPlatform;
  token: string;
}) => {
  // Convex mutation for updating project
  const updateProjectMutation = useMutation(api.projects.index.updateProject);

  return (
    <div>
      <div className="flex items-center justify-between flex-wrap gap-4 mb-4">
        <div className="flex items-center gap-3">
          <ProjectTypeSelector
            iconOnly
            selectedType={platform}
            size="lg"
            disabled={true}
          />
          <div>
            <InlineEditField
              displayValue={
                <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
              }
              value={title}
              onSave={async (value) => {
                try {
                  await updateProjectMutation({
                    id: id as Id<"Project">,
                    name: value,
                  });
                  toast.success("Project name updated");
                } catch (error) {
                  toast.error("Failed to update project");
                }
              }}
            />
          </div>
        </div>
        <div className="flex items-center  gap-4">
          <Button variant={"dark"} asChild>
            <Link href={`/project/${id}/board`}>Project Board</Link>
          </Button>
        </div>
      </div>

      <InlineEditTextArea
        value={description}
        onSave={async (value) => {
          try {
            await updateProjectMutation({
              id: id as Id<"Project">,
              description: value,
            });
            toast.success("Project description updated");
          } catch (error) {
            toast.error("Failed to update project");
          }
        }}
      />
    </div>
  );
};
