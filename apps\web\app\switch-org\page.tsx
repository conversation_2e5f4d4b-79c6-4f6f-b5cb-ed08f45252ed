import React from "react";
import SwitchOrg from "./SwitchOrg";
import { fetchQuery } from "convex/nextjs";
import { api } from "@workspace/backend";

const OrgSelection = async () => {
  //

  const [orgs, invitations] = await Promise.all([
    fetchQuery(api.auth.getOrganizations),
    fetchQuery(api.auth.getInvitations),
  ]);

  return (
    <div className="my-10 w-full min-h-screen mx-auto px-5">
      <SwitchOrg orgs={orgs} invitations={invitations} />
    </div>
  );
};

export default OrgSelection;
