import { v } from "convex/values";
import { query, mutation } from "../_generated/server";
import { betterAuth } from "../auth";

export const getAllIdeas = query({
  args: {},
  handler: async (ctx) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const ideas = await ctx.db
      .query("Idea")
      .withIndex("by_organization", (q) =>
        q.eq("organizationId", session.organizationId as any)
      )
      .collect();

    return ideas.map((idea) => ({
      id: idea._id,
      name: idea.name,
      description: idea.description,
      industry: idea.industry,
      status: idea.status,
      problemSolved: idea.problemSolved,
      solutionOffered: idea.solutionOffered,
      aiOverallValidation: idea.aiOverallValidation,
      internal: idea.internal,
      openSource: idea.openSource,
      ownerId: idea.ownerId,
      organizationId: idea.organizationId,
      _creationTime: idea._creationTime,
    }));
  },
});

export const getIdea = query({
  args: { id: v.id("Idea") },
  handler: async (ctx, { id }) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const idea = await ctx.db.get(id);

    if (!idea) {
      throw new Error("Idea not found");
    }

    // Ensure user can only access ideas from their organization
    if (idea.organizationId !== session.organizationId) {
      throw new Error("Unauthorized access to idea");
    }

    return {
      id: idea._id,
      name: idea.name,
      description: idea.description,
      industry: idea.industry,
      status: idea.status,
      problemSolved: idea.problemSolved,
      solutionOffered: idea.solutionOffered,
      aiOverallValidation: idea.aiOverallValidation,
      internal: idea.internal,
      openSource: idea.openSource,
      ownerId: idea.ownerId,
      organizationId: idea.organizationId,
      _creationTime: idea._creationTime,
    };
  },
});

export const createIdea = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    industry: v.string(),
    internal: v.boolean(),
    openSource: v.boolean(),
    status: v.optional(v.string()),
    problemSolved: v.optional(v.string()),
    solutionOffered: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const ideaId = await ctx.db.insert("Idea", {
      name: args.name,
      description: args.description,
      industry: args.industry,
      internal: args.internal,
      openSource: args.openSource,
      status: (args.status as any) || "INVALIDATED",
      problemSolved: args.problemSolved,
      solutionOffered: args.solutionOffered,
      ownerId: session.id,
      organizationId: session.organizationId as any,
    });

    return { id: ideaId };
  },
});

export const updateIdea = mutation({
  args: {
    id: v.id("Idea"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    industry: v.optional(v.string()),
    internal: v.optional(v.boolean()),
    openSource: v.optional(v.boolean()),
    status: v.optional(v.string()),
    problemSolved: v.optional(v.string()),
    solutionOffered: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const existingIdea = await ctx.db.get(args.id);

    if (!existingIdea) {
      throw new Error("Idea not found");
    }

    // Ensure user can only update ideas from their organization
    if (existingIdea.organizationId !== session.organizationId) {
      throw new Error("Unauthorized access to idea");
    }

    const updateData: any = {};
    
    if (args.name !== undefined) updateData.name = args.name;
    if (args.description !== undefined) updateData.description = args.description;
    if (args.industry !== undefined) updateData.industry = args.industry;
    if (args.internal !== undefined) updateData.internal = args.internal;
    if (args.openSource !== undefined) updateData.openSource = args.openSource;
    if (args.status !== undefined) updateData.status = args.status as any;
    if (args.problemSolved !== undefined) updateData.problemSolved = args.problemSolved;
    if (args.solutionOffered !== undefined) updateData.solutionOffered = args.solutionOffered;

    await ctx.db.patch(args.id, updateData);

    return { success: true };
  },
});

export const deleteIdea = mutation({
  args: { id: v.id("Idea") },
  handler: async (ctx, { id }) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const existingIdea = await ctx.db.get(id);

    if (!existingIdea) {
      throw new Error("Idea not found");
    }

    // Ensure user can only delete ideas from their organization
    if (existingIdea.organizationId !== session.organizationId) {
      throw new Error("Unauthorized access to idea");
    }

    // Check for related entities before deletion
    const relatedProjects = await ctx.db
      .query("Project")
      .withIndex("by_idea", (q) => q.eq("ideaId", id))
      .first();

    const relatedCompetitors = await ctx.db
      .query("Competitor")
      .withIndex("by_idea", (q) => q.eq("ideaId", id))
      .first();

    if (relatedProjects || relatedCompetitors) {
      throw new Error(
        "Cannot delete idea with existing projects or competitors. Please remove them first."
      );
    }

    await ctx.db.delete(id);

    return true;
  },
});