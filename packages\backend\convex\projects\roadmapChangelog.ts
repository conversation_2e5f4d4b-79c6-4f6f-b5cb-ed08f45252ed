import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { betterAuth } from "../auth";

export const getRoadmapChangelogsByRoadmap = query({
  args: {
    roadmapId: v.id("PublicRoadmap"),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    return await ctx.db
      .query("RoadmapChangelog")
      .filter((q) => q.eq(q.field("roadmapId"), args.roadmapId))
      .collect();
  },
});

// Public version for landing page - no auth required
export const getPublicRoadmapChangelogsByRoadmap = query({
  args: {
    roadmapId: v.id("PublicRoadmap"),
  },
  handler: async (ctx, args) => {
    // Verify the roadmap exists and is public
    const roadmap = await ctx.db.get(args.roadmapId);
    if (!roadmap || !roadmap.isPublic) {
      return [];
    }
    
    return await ctx.db
      .query("RoadmapChangelog")
      .filter((q) => q.eq(q.field("roadmapId"), args.roadmapId))
      .filter((q) => q.eq(q.field("isPublished"), true))
      .collect();
  },
});

export const createRoadmapChangelog = mutation({
  args: {
    roadmapId: v.id("PublicRoadmap"),
    title: v.string(),
    description: v.string(),
    version: v.optional(v.string()),
    publishDate: v.number(),
    isPublished: v.boolean(),
    fixes: v.array(v.string()),
    newFeatures: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    return await ctx.db.insert("RoadmapChangelog", {
      roadmapId: args.roadmapId,
      title: args.title,
      description: args.description,
      version: args.version,
      publishDate: args.publishDate,
      isPublished: args.isPublished,
      fixes: args.fixes,
      newFeatures: args.newFeatures,
    });
  },
});

export const updateRoadmapChangelog = mutation({
  args: {
    id: v.id("RoadmapChangelog"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    version: v.optional(v.string()),
    publishDate: v.optional(v.number()),
    isPublished: v.optional(v.boolean()),
    fixes: v.optional(v.array(v.string())),
    newFeatures: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    const { id, ...updates } = args;
    return await ctx.db.patch(id, updates);
  },
});



export const deleteRoadmapChangelog = mutation({
  args: {
    id: v.id("RoadmapChangelog"),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }

    const changelog = await ctx.db.get(args.id);
    if (!changelog) throw new Error("Changelog not found");

    // Verify the changelog belongs to a roadmap in the user's organization
    const roadmap = await ctx.db.get(changelog.roadmapId);
    if (!roadmap) throw new Error("Roadmap not found");

    const project = await ctx.db.get(roadmap.projectId);
    if (!project || project.organizationId !== identity.organizationId) {
      throw new Error("Unauthorized");
    }

    // Delete the changelog
    await ctx.db.delete(args.id);

    return { success: true };
  },
});
