"use client";

import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend";

export function LiveWaitlistStats({
  waitlistId,
}: {
  waitlistId: Id<"Waitlist">;
}) {
  // Use Convex useQuery for real-time updates
  const liveStats = useQuery(api.projects.waitlist.getLiveWaitlistStats, {
    waitlistId,
  });

  return (
    <div className="text-muted-foreground">
      <span className="font-semibold text-foreground">
        {liveStats?.totalEntries.toLocaleString()}+
      </span>{" "}
      people have already joined
    </div>
  );
}
