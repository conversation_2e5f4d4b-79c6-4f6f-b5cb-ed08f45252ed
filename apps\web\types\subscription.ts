// Convex subscription type (from database)
export interface ConvexSubscription {
  _id: string;
  status?: string;
  organisation_id: string;
  subscription_id?: string;
  product_id?: string;
  userId?: string;
}

// Frontend subscription type (for UI components)
export interface Subscription {
  id: string;
  userId: string;
  status: string;
  priceId: string;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
  customerId?: string;
  amount?: number;
  currency?: string;
  interval?: string;
}

// Helper function to convert Convex subscription to frontend subscription
export function convertConvexSubscription(convexSub: ConvexSubscription | null): Subscription | null {
  if (!convexSub) return null;
  
  return {
    id: convexSub._id,
    userId: convexSub.userId || "",
    status: convexSub.status || "inactive",
    priceId: convexSub.product_id || "",
    currentPeriodStart: new Date(), // You might want to add these fields to your schema
    currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    cancelAtPeriodEnd: false,
    customerId: convexSub.subscription_id,
    amount: 0, // Add to schema if needed
    currency: "usd", // Add to schema if needed
    interval: "month", // Add to schema if needed
  };
} 