"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, CheckCircle, <PERSON>hare2, Target } from "lucide-react";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import {
  MetricCard,
  ConversionFunnel,
  TrafficSources,
  StatusDistribution,
  ReferralPerformance,
  GrowthInsights,
} from "@/components/waitlist/analytics-metrics";

interface WaitlistAnalyticsProps {
  waitlistId: string;
}

export default function WaitlistAnalytics({
  waitlistId,
}: WaitlistAnalyticsProps) {
  // Use Convex hooks for real-time analytics data
  const analytics = useQuery(api.projects.waitlist.getWaitlistAnalytics, {
    waitlistId: waitlistId as Id<"Waitlist">,
  });

  const isLoading = analytics === undefined;

  if (isLoading || !analytics) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Main Metrics Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        <MetricCard
          icon={Users}
          title="Total Entries"
          value={analytics.totalEntries}
          subtitle={`+${analytics.recentEntries} this week`}
          iconColor="text-blue-500"
        />

        <MetricCard
          icon={CheckCircle}
          title="Verified"
          value={analytics.verifiedCount}
          subtitle={`${analytics.verificationRate}% verified`}
          iconColor="text-green-500"
        />

        <MetricCard
          icon={Share2}
          title="Referrals"
          value={analytics.totalReferrals}
          subtitle={`${analytics.avgReferralsPerUser.toFixed(1)} avg per user`}
          iconColor="text-purple-500"
        />

        <MetricCard
          icon={Target}
          title="Conversion"
          value={`${analytics.overallConversionRate.toFixed(1)}%`}
          subtitle={`${analytics.joinedCount} joined`}
          iconColor="text-orange-500"
        />
      </div>

      {/* Conversion Funnel */}
      <ConversionFunnel waitlistId={waitlistId} />

      {/* Traffic Sources & Status Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <TrafficSources waitlistId={waitlistId} />
        <StatusDistribution waitlistId={waitlistId} />
      </div>
    </div>
  );
}
