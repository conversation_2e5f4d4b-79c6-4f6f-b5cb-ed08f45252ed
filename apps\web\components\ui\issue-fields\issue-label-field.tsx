"use client";

import { IssueFieldBase } from "./issue-field-base";
import { labels } from "@/utils/constants/issues/labels";
import { IssueLabelBadge } from "@/components/project/issues/issue-badge";
import { useMutation } from "convex/react";
import { api, Id } from "@workspace/backend";

// Local IssueLabel type based on backend enum
export type IssueLabel =
  | "UI"
  | "BUG"
  | "FEATURE"
  | "IMPROVEMENT"
  | "TASK"
  | "DOCUMENTATION"
  | "REFACTOR"
  | "PERFORMANCE"
  | "DESIGN"
  | "SECURITY"
  | "ACCESSIBILITY"
  | "TESTING"
  | "INTERNATIONALIZATION";

interface IssueLabelFieldProps {
  issueId: string;
  value: IssueLabel;
  className?: string;
  disabled?: boolean;
  align?: "start" | "center" | "end";
  onChange?: (label: IssueLabel) => Promise<void>;
}

export function IssueLabelField({
  issueId,
  value,
  disabled,
  align,
  onChange,
}: IssueLabelFieldProps) {
  const updateIssueLabel = useMutation(api.issues.index.updateIssueLabel);

  const handleLabelChange = async (newValue: string | IssueLabel) => {
    const labelValue = typeof newValue === "string" ? (newValue as IssueLabel) : newValue;
    if (onChange) {
      await onChange(labelValue);
    } else {
      await updateIssueLabel({ id: issueId as Id<"Issue">, label: labelValue });
    }
  };

  return (
    <IssueFieldBase
      value={value}
      onSave={handleLabelChange}
      options={labels.map((l) => ({
        id: l.id,
        name: l.name,
        icon: l.icon,
        colorClass: l.colorClass,
      }))}
      displayValue={<IssueLabelBadge label={value} />}
      placeholder="Set label"
      searchPlaceholder="Search label..."
      emptyText="No label found."
      disabled={disabled}
      errorMessage="Failed to update label"
      align={align}
      className="w-fit"
    />
  );
}
