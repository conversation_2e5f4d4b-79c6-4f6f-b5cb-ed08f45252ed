{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**"]}, "start": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^lint"], "outputs": []}, "typecheck": {"dependsOn": ["^typecheck"], "outputs": []}, "test": {"dependsOn": ["^test"], "outputs": []}, "dev": {"cache": false, "persistent": true}}}