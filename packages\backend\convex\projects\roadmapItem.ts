import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { IssueStatus, IssueLabel, Importance } from "../../schema/enum";
import { betterAuth } from "../auth";

export const getRoadmapItemsByRoadmap = query({
  args: {
    roadmapId: v.id("PublicRoadmap"),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    return await ctx.db
      .query("RoadmapItem")
      .filter((q) => q.eq(q.field("roadmapId"), args.roadmapId))
      .collect();
  },
});

// Public version for landing page - no auth required
export const getPublicRoadmapItemsByRoadmap = query({
  args: {
    roadmapId: v.id("PublicRoadmap"),
  },
  handler: async (ctx, args) => {
    // Verify the roadmap exists and is public
    const roadmap = await ctx.db.get(args.roadmapId);
    if (!roadmap || !roadmap.isPublic) {
      return [];
    }
    
    return await ctx.db
      .query("RoadmapItem")
      .filter((q) => q.eq(q.field("roadmapId"), args.roadmapId))
      .filter((q) => q.eq(q.field("isPublic"), true))
      .collect();
  },
});

export const createRoadmapItem = mutation({
  args: {
    roadmapId: v.id("PublicRoadmap"),
    title: v.string(),
    description: v.string(),
    status: IssueStatus,
    category: IssueLabel,
    isPublic: v.boolean(),
    priority: Importance,
    targetDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    return await ctx.db.insert("RoadmapItem", {
      roadmapId: args.roadmapId,
      title: args.title,
      description: args.description,
      status: args.status,
      category: args.category,
      isPublic: args.isPublic,
      priority: args.priority,
      targetDate: args.targetDate,
    });
  },
});

export const updateRoadmapItem = mutation({
  args: {
    id: v.id("RoadmapItem"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    status: v.optional(IssueStatus),
    category: v.optional(IssueLabel),
    isPublic: v.optional(v.boolean()),
    priority: v.optional(Importance),
    targetDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    const { id, ...updates } = args;
    return await ctx.db.patch(id, updates);
  },
});

// Get roadmap items with enriched stats (vote/feedback counts)
export const getRoadmapItemsWithStats = query({
  args: {
    roadmapId: v.id("PublicRoadmap"),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }

    // Verify roadmap belongs to user's organization
    const roadmap = await ctx.db.get(args.roadmapId);
    if (!roadmap) throw new Error("Roadmap not found");

    const project = await ctx.db.get(roadmap.projectId);
    if (!project || project.organizationId !== identity.organizationId) {
      throw new Error("Unauthorized");
    }

    const items = await ctx.db
      .query("RoadmapItem")
      .filter((q) => q.eq(q.field("roadmapId"), args.roadmapId))
      .collect();

    // Enrich items with vote and feedback counts
    const enrichedItems = await Promise.all(
      items.map(async (item) => {
        const votes = await ctx.db
          .query("RoadmapVote")
          .filter((q) => q.eq(q.field("roadmapItemId"), item._id))
          .collect();

        const feedback = await ctx.db
          .query("RoadmapFeedback")
          .filter((q) => q.eq(q.field("roadmapItemId"), item._id))
          .collect();

        // Calculate positive feedback count
        const positiveFeedbackCount = feedback.filter(
          (fb) => fb.sentiment === "positive"
        ).length;

        return {
          ...item,
          voteCount: votes.length,
          feedbackCount: feedback.length,
          positiveFeedbackCount,
          createdAt: item._creationTime,
        };
      })
    );

    return enrichedItems;
  },
});



// Get roadmap statistics
export const getRoadmapStats = query({
  args: { roadmapId: v.id("PublicRoadmap") },
  handler: async (ctx, { roadmapId }) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }

    // Verify roadmap belongs to user's organization
    const roadmap = await ctx.db.get(roadmapId);
    if (!roadmap) throw new Error("Roadmap not found");

    const project = await ctx.db.get(roadmap.projectId);
    if (!project || project.organizationId !== identity.organizationId) {
      throw new Error("Unauthorized");
    }

    // Get all items with their feedback
    const items = await ctx.db
      .query("RoadmapItem")
      .filter((q) => q.eq(q.field("roadmapId"), roadmapId))
      .collect();

    let totalVotes = 0;
    let totalFeedback = 0;
    const statusCounts: Record<string, number> = {};
    const categoryCounts: Record<string, number> = {};

    for (const item of items) {
      // Count votes
      const votes = await ctx.db
        .query("RoadmapVote")
        .filter((q) => q.eq(q.field("roadmapItemId"), item._id))
        .collect();
      totalVotes += votes.length;

      // Count feedback
      const feedback = await ctx.db
        .query("RoadmapFeedback")
        .filter((q) => q.eq(q.field("roadmapItemId"), item._id))
        .collect();
      totalFeedback += feedback.length;

      // Count by status
      statusCounts[item.status] = (statusCounts[item.status] || 0) + 1;

      // Count by category
      categoryCounts[item.category] = (categoryCounts[item.category] || 0) + 1;
    }

    // Get changelogs count
    const changelogs = await ctx.db
      .query("RoadmapChangelog")
      .filter((q) => q.eq(q.field("roadmapId"), roadmapId))
      .collect();

    // Get feature requests count
    const featureRequests = await ctx.db
      .query("FeatureRequest")
      .filter((q) => q.eq(q.field("roadmapId"), roadmapId))
      .collect();

    return {
      totalItems: items.length,
      totalChangelogs: changelogs.length,
      totalFeatureRequests: featureRequests.length,
      statusCounts,
      categoryCounts,
      totalVotes,
      totalFeedback,
      lastUpdated: roadmap._creationTime,
    };
  },
});

// Get a single roadmap item with all its details (votes, feedback, etc.)
export const getRoadmapItemWithDetails = query({
  args: {
    id: v.id("RoadmapItem"),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }

    const item = await ctx.db.get(args.id);
    if (!item) return null;

    // Verify the item belongs to a roadmap in the user's organization
    const roadmap = await ctx.db.get(item.roadmapId);
    if (!roadmap) throw new Error("Roadmap not found");

    const project = await ctx.db.get(roadmap.projectId);
    if (!project || project.organizationId !== identity.organizationId) {
      throw new Error("Unauthorized");
    }

    // Get votes for this item
    const votes = await ctx.db
      .query("RoadmapVote")
      .filter((q) => q.eq(q.field("roadmapItemId"), args.id))
      .collect();

    // Get feedback for this item
    const feedback = await ctx.db
      .query("RoadmapFeedback")
      .filter((q) => q.eq(q.field("roadmapItemId"), args.id))
      .collect();

    // Calculate positive feedback count
    const positiveFeedbackCount = feedback.filter(
      (fb) => fb.sentiment === "positive"
    ).length;

    return {
      ...item,
      voteCount: votes.length,
      feedbackCount: feedback.length,
      positiveFeedbackCount,
      votes,
      feedback,
      createdAt: item._creationTime,
    };
  },
});

export const deleteRoadmapItem = mutation({
  args: {
    id: v.id("RoadmapItem"),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }

    const item = await ctx.db.get(args.id);
    if (!item) throw new Error("Roadmap item not found");

    // Verify the item belongs to a roadmap in the user's organization
    const roadmap = await ctx.db.get(item.roadmapId);
    if (!roadmap) throw new Error("Roadmap not found");

    const project = await ctx.db.get(roadmap.projectId);
    if (!project || project.organizationId !== identity.organizationId) {
      throw new Error("Unauthorized");
    }

    // Delete all votes for this item
    const votes = await ctx.db
      .query("RoadmapVote")
      .filter((q) => q.eq(q.field("roadmapItemId"), args.id))
      .collect();
    for (const vote of votes) {
      await ctx.db.delete(vote._id);
    }

    // Delete all feedback for this item
    const feedback = await ctx.db
      .query("RoadmapFeedback")
      .filter((q) => q.eq(q.field("roadmapItemId"), args.id))
      .collect();
    for (const fb of feedback) {
      await ctx.db.delete(fb._id);
    }

    // Delete the item
    await ctx.db.delete(args.id);

    return { success: true };
  },
});
