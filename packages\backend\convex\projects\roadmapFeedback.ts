import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { betterAuth } from "../auth";
import { RoadmapFeedbackSentiment, FeaturePhase, Importance, IssueStatus, IssueLabel } from "../../schema/enum";

export const getRoadmapFeedbackByRoadmapItem = query({
  args: {
    roadmapItemId: v.id("RoadmapItem"),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    return await ctx.db
      .query("RoadmapFeedback")
      .filter((q) => q.eq(q.field("roadmapItemId"), args.roadmapItemId))
      .collect();
  },
});

export const createRoadmapFeedback = mutation({
  args: {
    roadmapItemId: v.id("RoadmapItem"),
    userId: v.optional(v.string()),
    ipAddress: v.string(),
    content: v.string(),
    sentiment: RoadmapFeedbackSentiment, // Assuming RoadmapFeedbackSentiment is a string
    isApproved: v.boolean(),
    convertedToFeatureId: v.optional(v.id("Feature")),
    convertedToIssueId: v.optional(v.id("Issue")),
    convertedAt: v.optional(v.number()),
    convertedBy: v.optional(v.string()),
    conversionNotes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // No authentication for feedback, as per typical public roadmap behavior
    return await ctx.db.insert("RoadmapFeedback", {
      roadmapItemId: args.roadmapItemId,
      userId: args.userId,
      ipAddress: args.ipAddress,
      content: args.content,
      sentiment: args.sentiment,
      isApproved: args.isApproved,
      convertedToFeatureId: args.convertedToFeatureId,
      convertedToIssueId: args.convertedToIssueId,
      convertedAt: args.convertedAt,
      convertedBy: args.convertedBy,
      conversionNotes: args.conversionNotes,
      createdAt: Date.now(),
    });
  },
});


export const updateRoadmapFeedback = mutation({
  args: {
    id: v.id("RoadmapFeedback"),
    content: v.optional(v.string()),
    sentiment: RoadmapFeedbackSentiment,
    isApproved: v.optional(v.boolean()),
    convertedToFeatureId: v.optional(v.id("Feature")),
    convertedToIssueId: v.optional(v.id("Issue")),
    convertedAt: v.optional(v.number()),
    convertedBy: v.optional(v.string()),
    conversionNotes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    const { id, ...updates } = args;
    return await ctx.db.patch(id, updates);
  },
});

export const deleteRoadmapFeedback = mutation({
  args: {
    id: v.id("RoadmapFeedback"),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    await ctx.db.delete(args.id);
    return { success: true };
  },
});

// Moderate feedback (approve/reject)
export const moderateFeedback = mutation({
  args: {
    id: v.id("RoadmapFeedback"),
    isApproved: v.boolean(),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }

    await ctx.db.patch(args.id, {
      isApproved: args.isApproved,
    });

    return { success: true };
  },
});

// Convert feedback to feature
export const convertFeedbackToFeature = mutation({
  args: {
    feedbackId: v.id("RoadmapFeedback"),
    featureName: v.string(),
    featureDescription: v.string(),
    priority: Importance,
    phase: FeaturePhase,
    conversionNotes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }

    // Get the feedback and verify ownership
    const feedback = await ctx.db.get(args.feedbackId);
    if (!feedback) {
      throw new Error("Feedback not found");
    }

    // Get the roadmap item to find the project
    const roadmapItem = await ctx.db.get(feedback.roadmapItemId);
    if (!roadmapItem) {
      throw new Error("Roadmap item not found");
    }

    // Get the roadmap to find the project
    const roadmap = await ctx.db.get(roadmapItem.roadmapId);
    if (!roadmap) {
      throw new Error("Roadmap not found");
    }

    // Verify the project belongs to the user's organization
    const project = await ctx.db.get(roadmap.projectId);
    if (!project || project.organizationId !== identity.organizationId) {
      throw new Error("Unauthorized");
    }

    // Create the feature
    const featureId = await ctx.db.insert("Feature", {
      name: args.featureName,
      description: args.featureDescription,
      priority: args.priority,
      phase: args.phase,
      projectId: roadmap.projectId,
      organizationId: identity.organizationId,
    });

    // Update the feedback to mark it as converted
    await ctx.db.patch(args.feedbackId, {
      convertedToFeatureId: featureId,
      convertedAt: Date.now(),
      convertedBy: identity.id,
      conversionNotes: args.conversionNotes,
    });

    return { success: true, featureId };
  },
});

// Convert feedback to issue
export const convertFeedbackToIssue = mutation({
  args: {
    feedbackId: v.id("RoadmapFeedback"),
    issueTitle: v.string(),
    issueDescription: v.string(),
    priority: Importance,
    status: IssueStatus,
    label: IssueLabel,
    conversionNotes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }

    // Get the feedback and verify ownership
    const feedback = await ctx.db.get(args.feedbackId);
    if (!feedback) {
      throw new Error("Feedback not found");
    }

    // Get the roadmap item to find the project
    const roadmapItem = await ctx.db.get(feedback.roadmapItemId);
    if (!roadmapItem) {
      throw new Error("Roadmap item not found");
    }

    // Get the roadmap to find the project
    const roadmap = await ctx.db.get(roadmapItem.roadmapId);
    if (!roadmap) {
      throw new Error("Roadmap not found");
    }

    // Verify the project belongs to the user's organization
    const project = await ctx.db.get(roadmap.projectId);
    if (!project || project.organizationId !== identity.organizationId) {
      throw new Error("Unauthorized");
    }

    // Create the issue
    const issueId = await ctx.db.insert("Issue", {
      title: args.issueTitle,
      description: args.issueDescription,
      priority: args.priority,
      status: args.status,
      label: args.label,
      projectId: roadmap.projectId,
      organizationId: identity.organizationId,
      sourceType: "feedback",
    });

    // Update the feedback to mark it as converted
    await ctx.db.patch(args.feedbackId, {
      convertedToIssueId: issueId,
      convertedAt: Date.now(),
      convertedBy: identity.id,
      conversionNotes: args.conversionNotes,
    });

    return { success: true, issueId };
  },
});
