import { useQuery, useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { toast } from "sonner";

export function useIssueLinks(issueId: string) {
  const issueConvexId = issueId as Id<"Issue">;

  const links = useQuery(api.issues.index.getIssueLinks, {
    issueId: issueConvexId,
  });

  const createLinkMutation = useMutation(api.issues.index.addIssueLink);
  const deleteLinkMutation = useMutation(api.issues.index.deleteIssueLink);

  const createLink = async ({ url, issueId }: { url: string; issueId: string }) => {
    try {
      const result = await createLinkMutation({
        issueId: issueId as Id<"Issue">,
        url,
      });
      return { success: true };
    } catch (error) {
      console.error("Error creating link:", error);
      toast.error("Failed to add link");
      return { success: false, error };
    }
  };

  const deleteLink = async ({ id }: { id: string }) => {
    try {
      await deleteLinkMutation({ linkId: id as Id<"IssueLink"> });
      return { success: true };
    } catch (error) {
      console.error("Error deleting link:", error);
      toast.error("Failed to remove link");
      return { success: false, error };
    }
  };

  return {
    links: links || [],
    isLoading: links === undefined,
    createLink,
    isCreating: false,
    deleteLink,
    isDeleting: false,
  };
}
