import Header from "@/components/shared/header";
import WaitlistManager from "./_components/waitlist-manager";
import { Edit2 } from "lucide-react";
import Link from "next/link";
import { ExternalLink } from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";

interface WaitlistPageProps {
  params: Promise<{ id: string }>;
}

export default async function WaitlistPage({ params }: WaitlistPageProps) {
  const { id } = await params;

  return (
    <div>
      <Header
        crumb={[
          { title: "Waitlist", url: "/waitlist" },
          { title: "Manage waitlist", url: `/waitlist/${id}` },
        ]}
      >
        <div className="flex items-center gap-2 ">
          <Button size="icon" asChild>
            <Link
              href={`https://rayai.dev/wl/placeholder`}
              target="_blank"
            >
              <ExternalLink className="h-4 w-4" />
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href={`/waitlist/${id}/edit`}>
              <Edit2 className="mr-2 h-4 w-4" />
              Edit
            </Link>
          </Button>
        </div>
      </Header>
      <WaitlistManager waitlistId={id} />
    </div>
  );
}
