import { MilestoneList } from "@/components/project/milestones/milestone-list";

interface ProjectMilestonesPageProps {
  params: Promise<{ id: string }>;
}

export default async function ProjectMilestonesPage({
  params,
}: ProjectMilestonesPageProps) {
  const projectId = (await params).id;

  return (
    <div className="container mx-auto px-4 py-6">
      <MilestoneList projectId={projectId} />
    </div>
  );
}
