import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import {
  Project,
  ActivityFeed,
  Asset,
  AssetDownload,
  AssetView,
  Issue,
  IssueDependency,
  IssueLink,
  Milestone,
  MilestoneDependency,
  PublicRoadmap,
  Referral,
  Waitlist,
  WaitlistEntry,
  Feature,
  FeatureDependency,
  FeatureLink,
  ChangelogEntry,
  FeatureRequest,
  RoadmapChangelog,
  RoadmapFeedback,
  RoadmapItem,
  RoadmapVote,
} from "../schema/project";
import {
  CompetitiveMove,
  Competitor,
  CompetitorSwot,
  Idea,
} from "../schema/idea";

import {
  ApiCall,
  ApiKey,
  Integration,
  IntegrationUsage,
} from "../schema/settings";

import {
  ideaValidation,
  validationMetrics,
  marketValidation,
  marketInsight,
  marketRegionScore,
  businessValidation,
  businessInsight,
  riskAnalysis,
  riskItem,
  productMarketFitAnalysis,
  pmfMetric,
  pmfFeedback,
  monthlyProjection,
  acquisitionChannel,
  pricingTier,
  competitorPricing,
  customerJourneyMapping,
  journeyStage,
  touchpoint,
  journeyPainPoint,
  targetAudienceSegmentation,
  audienceSegment,
  marketTrendAnalysis,
  marketTrend,
  customerNeedAnalysis,
  customerNeed,
  painPoint,
  pricingStrategyAnalysis,
} from "../schema/validation";

export default defineSchema({
  //AUTH
  User: defineTable({
    name: v.string(),
    email: v.string(),
    emailVerified: v.boolean(),
    image: v.optional(v.string()),
    role: v.optional(v.string()),
    twoFactorEnabled: v.optional(v.boolean()),
  }).index("by_email", ["email"]),

  Subscription: defineTable({
    status: v.optional(v.string()),
    organisation_id: v.id("Organization"),
    subscription_id: v.optional(v.string()),
    product_id: v.optional(v.string()),
    userId: v.optional(v.id("User")),
  })
    .index("by_organisation_id", ["organisation_id"])
    .index("by_userId", ["userId"]),

  // PROJECTS
  Project,
  ActivityFeed,
  Asset,
  AssetDownload,
  AssetView,
  Feature,
  FeatureDependency,
  FeatureLink,
  Issue,
  IssueDependency,
  IssueLink,
  Milestone,
  MilestoneDependency,
  PublicRoadmap,
  Referral,
  ChangelogEntry,
  FeatureRequest,
  RoadmapChangelog,
  RoadmapFeedback,
  RoadmapItem,
  RoadmapVote,
  Waitlist,
  WaitlistEntry,

  //   IDEAS
  CompetitiveMove,
  Competitor,
  CompetitorSwot,
  Idea,

  //SETTINGS
  ApiCall,
  ApiKey,
  Integration,
  IntegrationUsage,

  //VALIDATION
  ideaValidation,
  validationMetrics,
  marketValidation,
  marketInsight,
  marketRegionScore,
  businessValidation,
  businessInsight,
  riskAnalysis,
  riskItem,
  productMarketFitAnalysis,
  pmfMetric,
  pmfFeedback,
  monthlyProjection,
  acquisitionChannel,
  pricingTier,
  competitorPricing,
  customerJourneyMapping,
  journeyStage,
  touchpoint,
  journeyPainPoint,
  targetAudienceSegmentation,
  audienceSegment,
  marketTrendAnalysis,
  marketTrend,
  customerNeedAnalysis,
  customerNeed,
  painPoint,
  pricingStrategyAnalysis,

  // COMMENTS
  CommentThread: defineTable({
    roomId: v.string(),
    organizationId: v.string(),
    title: v.optional(v.string()),
    createdBy: v.id("User"),
    resolved: v.boolean(),
    resolvedBy: v.optional(v.id("User")),
    createdAt: v.number(),
    lastActivityAt: v.number(),
  }).index("by_roomId", ["roomId"]).index("by_org", ["organizationId"]).index("by_room_lastActivity", ["roomId", "lastActivityAt"]),

  Comment: defineTable({
    roomId: v.string(),
    organizationId: v.string(),
    threadId: v.id("CommentThread"),
    authorId: v.id("User"),
    text: v.string(),
    mentions: v.array(v.string()), // userIds
    createdAt: v.number(),
    editedAt: v.optional(v.number()),
    deletedAt: v.optional(v.number()),
  })
    .index("by_threadId", ["threadId"]) 
    .index("by_room_createdAt", ["roomId", "createdAt"]) 
    .index("by_org", ["organizationId"]),

  CommentAttachment: defineTable({
    roomId: v.string(),
    organizationId: v.string(),
    threadId: v.id("CommentThread"),
    commentId: v.id("Comment"),
    url: v.string(),
    name: v.string(),
    mime: v.string(),
    size: v.number(),
    createdAt: v.number(),
  }).index("by_commentId", ["commentId"]).index("by_threadId", ["threadId"]).index("by_roomId", ["roomId"]),

  CommentSubscription: defineTable({
    roomId: v.string(),
    organizationId: v.string(),
    threadId: v.optional(v.id("CommentThread")),
    userId: v.id("User"),
    createdAt: v.number(),
  })
    .index("by_room_user", ["roomId", "userId"]) 
    .index("by_thread_user", ["threadId", "userId"]) 
    .index("by_user", ["userId"]),

  CommentReaction: defineTable({
    commentId: v.id("Comment"),
    userId: v.id("User"),
    emoji: v.string(),
    createdAt: v.number(),
  })
    .index("by_commentId", ["commentId"]) 
    .index("by_comment_emoji", ["commentId", "emoji"]) 
    .index("by_user", ["userId"]),

  Notification: defineTable({
    userId: v.id("User"),
    type: v.union(v.literal("mention"), v.literal("comment"), v.literal("reply"), v.literal("thread_resolved")),
    roomId: v.string(),
    threadId: v.optional(v.id("CommentThread")),
    commentId: v.optional(v.id("Comment")),
    fromUserId: v.id("User"),
    read: v.boolean(),
    createdAt: v.number(),
    meta: v.optional(v.string()), // JSON string for extra details
  }).index("by_user_createdAt", ["userId", "createdAt"]).index("by_user_read", ["userId", "read"]),
});
