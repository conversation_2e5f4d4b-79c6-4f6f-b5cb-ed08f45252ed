"use client";

import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { RoadmapKanban } from "./roadmap-kanban";
import { AddItemDialog } from "./add-item-dialog";
import { EditRoadmapDialog } from "./edit-roadmap-dialog";
import LoadingSpinner from "@workspace/ui/components/loading-spinner";
import { StatusType } from "@/utils/constants/issues/status";

interface RoadmapDetailClientProps {
  roadmapId: string;
}

export function RoadmapDetailClient({ roadmapId }: RoadmapDetailClientProps) {
  const [showAddItemDialog, setShowAddItemDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [addItemInitialStatus, setAddItemInitialStatus] =
    useState<StatusType>("IN_REVIEW");

  // Fetch roadmap data using Convex
  const roadmap = useQuery(api.projects.roadmap.getRoadmapById, {
    id: roadmapId as any,
  });

  // Fetch roadmap items using Convex
  const items = useQuery(api.projects.roadmapItem.getRoadmapItemsByRoadmap, {
    roadmapId: roadmapId as any,
  });

  // Handle add item with status
  const handleAddItemWithStatus = (status: StatusType) => {
    setAddItemInitialStatus(status);
    setShowAddItemDialog(true);
  };

  // Show loading while data is being fetched
  if (roadmap === undefined || items === undefined) {
    return <LoadingSpinner />;
  }

  return (
    <div className="flex-1 flex flex-col">
      <RoadmapKanban
        roadmapId={roadmapId}
        onAddItem={handleAddItemWithStatus}
      />

      {/* Dialogs */}
      <AddItemDialog
        isOpen={showAddItemDialog}
        onClose={() => setShowAddItemDialog(false)}
        roadmapId={roadmapId}
        initialStatus={addItemInitialStatus}
      />

      <EditRoadmapDialog
        isOpen={showEditDialog}
        onClose={() => setShowEditDialog(false)}
        roadmapId={roadmapId}
        roadmap={{
          name: roadmap?.name || "",
          slug: roadmap?.slug || "",
          description: roadmap?.description || "",
          isPublic: roadmap?.isPublic || false,
          allowVoting: roadmap?.allowVoting || false,
          allowFeedback: roadmap?.allowFeedback || false,
          project: roadmap?.projectId || "",
        }}
      />
    </div>
  );
}
