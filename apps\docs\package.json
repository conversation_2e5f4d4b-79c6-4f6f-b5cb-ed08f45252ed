{"name": "docs", "version": "0.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev --turbo -p 3001", "start": "next start -p 3001", "postinstall": "fumadocs-mdx", "lint": "biome check .", "format": "biome format . --write", "typecheck": "tsc --noEmit", "test": "vitest run"}, "dependencies": {"fumadocs-core": "15.6.6", "fumadocs-mdx": "11.7.1", "fumadocs-ui": "15.6.6", "lucide-react": "^0.528.0", "next": "15.4.4", "react": "^19.1.1", "react-dom": "^19.1.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/mdx": "^2.0.13", "@types/node": "24.1.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "^5.9.2"}}