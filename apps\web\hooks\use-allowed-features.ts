import { useMemo } from "react";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Feature, GeneralFeature } from "@/types/features";

export type AllowedFeatures = Feature[];

export const useAllowedFeatures = () => {
  // Note: This would need a corresponding Convex function to be implemented
  // For now, using a placeholder that returns empty data
  const data = useQuery(api.account?.getAllowedFeatures) || [];
  const isLoading = data === undefined;
  const isError = false;

  const hasFeature = useMemo(() => {
    return (feature: Feature) => {
      if (!data) return false;
      return data.includes(feature);
    };
  }, [data]);

  return {
    allowedFeatures: data || [],
    isLoading,
    isError,
    hasFeature,
  };
};
