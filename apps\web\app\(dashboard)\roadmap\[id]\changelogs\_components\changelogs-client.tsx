"use client";

import { useQuery } from "convex/react";
import React, { useState } from "react";
import { api } from "@workspace/backend/convex/_generated/api";
import { ChangelogDialog } from "./changelog-dialog";
import { RoadmapChangelogs } from "./roadmap-changelogs";

interface ChangelogsClientProps {
  roadmapId: string;
}

export function ChangelogsClient({ roadmapId }: ChangelogsClientProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Use Convex queries
  const roadmap = useQuery(api.projects.roadmap.getRoadmapById, {
    id: roadmapId as any,
  });

  const changelogs = useQuery(api.projects.roadmapChangelog.getRoadmapChangelogsByRoadmap, {
    roadmapId: roadmapId as any,
  });

  const handleCreateChangelog = () => {
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
  };

  if (roadmap === undefined || changelogs === undefined) {
    return <div>Loading...</div>;
  }

  if (!roadmap) {
    return <div>Roadmap not found</div>;
  }

  return (
    <div>
      <RoadmapChangelogs
        changelogs={changelogs || []}
        onCreateChangelog={handleCreateChangelog}
        roadmapId={roadmapId}
        projectId={roadmap.projectId}
      />

      <ChangelogDialog
        isOpen={isDialogOpen}
        onClose={handleCloseDialog}
        roadmapId={roadmapId}
      />
    </div>
  );
}
