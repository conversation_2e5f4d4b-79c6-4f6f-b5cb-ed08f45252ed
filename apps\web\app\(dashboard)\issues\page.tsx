import AllIssues from "@/components/project/issues/all-issues";
import { NewIssue } from "@/components/project/issues/new-issue";
import Header from "@/components/shared/header";
import React from "react";

const IssuesPage = () => {
  return (
    <>
      <Header crumb={[{ title: "Issues", url: "/issues" }]}>
        <NewIssue />
      </Header>
      <AllIssues />
    </>
  );
};

export default IssuesPage;
