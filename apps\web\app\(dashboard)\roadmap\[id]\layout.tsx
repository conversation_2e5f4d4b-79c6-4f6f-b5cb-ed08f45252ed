import { Badge } from "@workspace/ui/components/badge";
import Link from "next/link";
import React from "react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { ExternalLink } from "lucide-react";
import { RoadmapTabs } from "./_components/tabs";
import { RoadmapForm } from "../components/new-roadmap";
import Header from "@/components/shared/header";
import { RoadmapLayoutClient } from "./_components/roadmap-layout-client";

export default async function RoadmapLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ id: string }>;
}) {
  const roadmapId = (await params).id;

  return <RoadmapLayoutClient roadmapId={roadmapId}>{children}</RoadmapLayoutClient>;
}
