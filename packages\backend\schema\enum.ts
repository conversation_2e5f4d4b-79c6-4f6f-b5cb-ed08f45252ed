import { v } from "convex/values";

export const ActivityType = v.union(
  v.literal("CREATED"),
  v.literal("UPDATED"),
  v.literal("PHASE_CHANGED"),
  v.literal("ASSIGNED"),
  v.literal("UNASSIGNED"),
  v.literal("DEPENDENCY_ADDED"),
  v.literal("DEPENDENCY_REMOVED"),
  v.literal("LINK_ADDED"),
  v.literal("LINK_REMOVED"),
  v.literal("PARENT_CHANGED")
);

export const EntityType = v.union(
  v.literal("PROJECT"),
  v.literal("FEATURE"),
  v.literal("ISSUE"),
  v.literal("IDEA"),
  v.literal("ROADMAP"),
  v.literal("MILESTONE")
);

export const IdeaStatus = v.union(
  v.literal("INVALIDATED"),
  v.literal("VALIDATED"),
  v.literal("FAILED"),
  v.literal("IN_PROGRESS"),
  v.literal("LAUNCHED")
);

export const Importance = v.union(
  v.literal("CRITICAL"),
  v.literal("HIGH"),
  v.literal("MEDIUM"),
  v.literal("LOW")
);

export const ProjectPlatform = v.union(
  v.literal("web"),
  v.literal("mobile"),
  v.literal("both"),
  v.literal("api"),
  v.literal("plugin"),
  v.literal("desktop"),
  v.literal("cli")
);

export const ProjectStatus = v.union(
  v.literal("planning"),
  v.literal("in_progress"),
  v.literal("review"),
  v.literal("completed")
);

export const IssueStatus = v.union(
  v.literal("BACKLOG"),
  v.literal("IN_PROGRESS"),
  v.literal("IN_REVIEW"),
  v.literal("DONE"),
  v.literal("BLOCKED"),
  v.literal("CANCELLED")
);

export const IssueLabel = v.union(
  v.literal("UI"),
  v.literal("BUG"),
  v.literal("FEATURE"),
  v.literal("IMPROVEMENT"),
  v.literal("TASK"),
  v.literal("DOCUMENTATION"),
  v.literal("REFACTOR"),
  v.literal("PERFORMANCE"),
  v.literal("DESIGN"),
  v.literal("SECURITY"),
  v.literal("ACCESSIBILITY"),
  v.literal("TESTING"),
  v.literal("INTERNATIONALIZATION")
);

export const AssetType = v.union(
  v.literal("image"),
  v.literal("document"),
  v.literal("video"),
  v.literal("link"),
  v.literal("code"),
  v.literal("design"),
  v.literal("other")
);

export const LinkType = v.union(
  v.literal("youtube"),
  v.literal("figma"),
  v.literal("notion"),
  v.literal("github"),
  v.literal("dribbble"),
  v.literal("behance"),
  v.literal("external")
);

export const AssetCategory = v.union(
  v.literal("branding"),
  v.literal("ui_design"),
  v.literal("mockups"),
  v.literal("documentation"),
  v.literal("inspiration"),
  v.literal("code_snippets"),
  v.literal("presentations"),
  v.literal("tutorials"),
  v.literal("other")
);

export const RoadmapFeedbackSentiment = v.union(
  v.literal("positive"),
  v.literal("neutral"),
  v.literal("negative")
);

export const FeatureRequestStatus = v.union(
  v.literal("pending"),
  v.literal("under_review"),
  v.literal("approved"),
  v.literal("rejected"),
  v.literal("implemented")
);

export const FeatureRequestPriority = v.union(
  v.literal("low"),
  v.literal("medium"),
  v.literal("high"),
  v.literal("urgent")
);

export const FeaturePhase = v.union(
  v.literal("DISCOVERY"),
  v.literal("PLANNING"),
  v.literal("DEVELOPMENT"),
  v.literal("TESTING"),
  v.literal("DEPLOYMENT"),
  v.literal("COMPLETED"),
  v.literal("RELEASE"),
  v.literal("LIVE"),
  v.literal("DEPRECATED")
);

export const MilestoneStatus = v.union(
  v.literal("NOT_STARTED"),
  v.literal("IN_PROGRESS"),
  v.literal("AT_RISK"),
  v.literal("COMPLETED"),
  v.literal("DELAYED")
);

export const IntegrationType = v.union(
  v.literal("RESEND"),
  v.literal("LOOPS"),
  v.literal("SENDGRID"),
  v.literal("MAILCHIMP"),
  v.literal("CONVERTKIT"),
  v.literal("GITHUB")
);

export const ApiPermission = v.union(
  v.literal("READ"),
  v.literal("WRITE"),
  v.literal("DELETE"),
  v.literal("ADMIN")
);

export const ChangelogEntryType = v.union(
  v.literal("FEATURE"),
  v.literal("FIX"),
  v.literal("IMPROVEMENT"),
  v.literal("BREAKING"),
  v.literal("SECURITY"),
  v.literal("DEPRECATION"),
  v.literal("DOCUMENTATION"),
  v.literal("PERFORMANCE")
);

export const SwotType = v.union(
  v.literal("Strength"),
  v.literal("Weakness"),
  v.literal("Opportunity"),
  v.literal("Threat")
);

// Validation System Enums
export const ValidationStatus = v.union(
  v.literal("PENDING"),
  v.literal("IN_PROGRESS"),
  v.literal("COMPLETED"),
  v.literal("FAILED"),
  v.literal("REQUIRES_REVIEW")
);

export const WaitlistEntryStatus = v.union(
  v.literal("PENDING"),
  v.literal("VERIFIED"),
  v.literal("INVITED"),
  v.literal("JOINED")
);

export const ValidationScore = v.union(
  v.literal("EXCELLENT"),
  v.literal("GOOD"),
  v.literal("AVERAGE"),
  v.literal("POOR"),
  v.literal("CRITICAL")
);

export const MarketSize = v.union(
  v.literal("NICHE"),
  v.literal("SMALL"),
  v.literal("MEDIUM"),
  v.literal("LARGE"),
  v.literal("MASSIVE")
);

export const MarketGrowthRate = v.union(
  v.literal("DECLINING"),
  v.literal("STAGNANT"),
  v.literal("SLOW_GROWTH"),
  v.literal("MODERATE_GROWTH"),
  v.literal("RAPID_GROWTH"),
  v.literal("EXPLOSIVE_GROWTH")
);

export const MarketMaturity = v.union(
  v.literal("EMERGING"),
  v.literal("GROWTH"),
  v.literal("MATURE"),
  v.literal("DECLINING"),
  v.literal("TRANSFORMING")
);

export const CompetitionLevel = v.union(
  v.literal("NONE"),
  v.literal("LOW"),
  v.literal("MODERATE"),
  v.literal("HIGH"),
  v.literal("SATURATED")
);

export const CustomerSegment = v.union(
  v.literal("B2B_ENTERPRISE"),
  v.literal("B2B_SMB"),
  v.literal("B2C_CONSUMER"),
  v.literal("B2C_PROSUMER"),
  v.literal("B2G_GOVERNMENT"),
  v.literal("MARKETPLACE"),
  v.literal("PLATFORM")
);

export const RevenueModel = v.union(
  v.literal("SUBSCRIPTION"),
  v.literal("FREEMIUM"),
  v.literal("ONE_TIME_PURCHASE"),
  v.literal("TRANSACTION_FEE"),
  v.literal("ADVERTISING"),
  v.literal("COMMISSION"),
  v.literal("LICENSING"),
  v.literal("USAGE_BASED"),
  v.literal("HYBRID")
);

export const PricingStrategy = v.union(
  v.literal("PENETRATION"),
  v.literal("SKIMMING"),
  v.literal("COMPETITIVE"),
  v.literal("VALUE_BASED"),
  v.literal("COST_PLUS"),
  v.literal("DYNAMIC")
);

export const RiskLevel = v.union(
  v.literal("MINIMAL"),
  v.literal("LOW"),
  v.literal("MODERATE"),
  v.literal("HIGH"),
  v.literal("EXTREME")
);

export const RiskCategory = v.union(
  v.literal("MARKET"),
  v.literal("TECHNICAL"),
  v.literal("FINANCIAL"),
  v.literal("REGULATORY"),
  v.literal("COMPETITIVE"),
  v.literal("OPERATIONAL"),
  v.literal("TEAM"),
  v.literal("TIMING")
);

export const FundingStage = v.union(
  v.literal("BOOTSTRAPPED"),
  v.literal("PRE_SEED"),
  v.literal("SEED"),
  v.literal("SERIES_A"),
  v.literal("SERIES_B"),
  v.literal("SERIES_C_PLUS"),
  v.literal("IPO_READY")
);

export const InvestmentRecommendation = v.union(
  v.literal("STRONG_BUY"),
  v.literal("BUY"),
  v.literal("HOLD"),
  v.literal("WEAK_BUY"),
  v.literal("AVOID")
);

export const GoToMarketStrategy = v.union(
  v.literal("DIRECT_SALES"),
  v.literal("INBOUND_MARKETING"),
  v.literal("OUTBOUND_MARKETING"),
  v.literal("PARTNERSHIPS"),
  v.literal("VIRAL_GROWTH"),
  v.literal("COMMUNITY_DRIVEN"),
  v.literal("PRODUCT_LED_GROWTH"),
  v.literal("CHANNEL_PARTNERS")
);

export const ValidationMethod = v.union(
  v.literal("SURVEYS"),
  v.literal("INTERVIEWS"),
  v.literal("LANDING_PAGE_TEST"),
  v.literal("MVP_TEST"),
  v.literal("PROTOTYPE_TEST"),
  v.literal("MARKET_RESEARCH"),
  v.literal("COMPETITOR_ANALYSIS"),
  v.literal("EXPERT_CONSULTATION")
);

export const FinancialMetric = v.union(
  v.literal("CAC"),
  v.literal("LTV"),
  v.literal("ARPU"),
  v.literal("MRR"),
  v.literal("ARR"),
  v.literal("CHURN_RATE"),
  v.literal("GROSS_MARGIN"),
  v.literal("BURN_RATE"),
  v.literal("RUNWAY"),
  v.literal("BREAK_EVEN")
);
