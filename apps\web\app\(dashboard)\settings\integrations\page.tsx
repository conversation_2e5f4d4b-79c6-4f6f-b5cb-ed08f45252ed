import { IntegrationsClient } from "./integrations-client";
import { Suspense } from "react";

export default async function IntegrationsPage({
  searchParams,
}: {
  searchParams: Promise<{ success?: string; error?: string }>;
}) {
  const { success, error } = await searchParams;

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <IntegrationsClient successMessage={success} errorMessage={error} />
    </Suspense>
  );
}
