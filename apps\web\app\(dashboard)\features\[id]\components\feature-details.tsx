"use client";
import React, { useState } from "react";
import { InlineEditField } from "@workspace/ui/components/inline-field";
import Link from "next/link";
import { Badge } from "@workspace/ui/components/badge";
import { InlineEditTextArea } from "@workspace/ui/components/inline-textarea";
import { useSession } from "@/context/session-context";
import LoadingSpinner from "@workspace/ui/components/loading-spinner";
import NoData from "@/components/shared/no-data";
import { toast } from "sonner";
import { GitBranch, Clock, Plug, Inbox, File } from "lucide-react";
import { NewFeature } from "@/components/project/features/new-feature";
import { ActivityFeed } from "@/components/shared/activity-feed";
import FeatureDependencyManager from "@/components/project/features/feature-dependency-manager";
import { PhaseSelector } from "@/components/ui/selectors/phase-selector";
import { PrioritySelector } from "@/components/ui/selectors/priority-selector";
import { ScrollArea } from "@workspace/ui/components/scroll-area";
import { cn } from "@/lib/utils";
import { Room } from "@/components/liveblocks/room";
import Editor from "@/components/shared/editor";
import { Comments } from "@/components/comments/Comments";
import { useQuery, useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";

const FeatureDetails = ({ id }: { id: string }) => {
  const [view, setView] = useState<
    "details" | "dependencies" | "prd" | "activity"
  >("prd");

  const featureId = id as Id<"Feature">;

  // Fetch feature details using Convex
  const feature = useQuery(api.projects.feature.getFeatureById, {
    id: featureId,
  });

  // Fetch feature hierarchy using Convex
  const featureHierarchy = useQuery(api.projects.feature.getFeatureHierarchy, {
    featureId,
  });

  // Mutation for updating feature using Convex
  const updateFeatureMutation = useMutation(api.projects.feature.updateFeature);

  const handleUpdate = async (updates: any) => {
    if (!id) return;
    try {
      await updateFeatureMutation({
        id: featureId,
        ...updates,
      });
      toast.success("Feature updated");
    } catch (error) {
      toast.error("Failed to update feature");
    }
  };

  if (feature === undefined)
    return (
      <div className="flex items-center justify-center p-20">
        <LoadingSpinner />
      </div>
    );

  if (feature === null) return <NoData title="Feature not found" />;

  return (
    <div className="container space-y-4">
      <div className="flex items-center gap-2">
        <Badge variant="info">Feature</Badge>
        <Link
          href={`/project/${feature.projectId}`}
          className="text-sm text-muted-foreground hover:text-primary hover:underline transition-colors flex items-center gap-1"
        >
          <span>{feature?.project?.name}</span>
        </Link>
      </div>
      <div className="p-1 5">
        <InlineEditField
          displayValue={
            <h1 className="text-2xl font-semibold leading-tight">
              {feature.name}
            </h1>
          }
          value={feature.name}
          onSave={async (value) => {
            await handleUpdate({ name: value });
          }}
        />
      </div>
      <div className="mt-2">
        <InlineEditTextArea
          value={feature.description || ""}
          placeholder="Add a description..."
          onSave={async (value) => {
            await handleUpdate({ description: value || undefined });
          }}
        />
      </div>

      <div className="w-full border-y">
        <ScrollArea className="w-full whitespace-nowrap">
          <div className="flex flex-wrap w-full gap-4 p-4">
            <button
              onClick={() => setView("prd")}
              className={cn(
                "inline-flex gap-3 items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
                view === "prd"
                  ? "bg-primary text-primary-foreground shadow-sm"
                  : "hover:bg-muted hover:text-muted-foreground"
              )}
            >
              <File size={18} />
              Feature PRD
            </button>
            <button
              onClick={() => setView("details")}
              className={cn(
                "inline-flex gap-3 items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
                view === "details"
                  ? "bg-primary text-primary-foreground shadow-sm"
                  : "hover:bg-muted hover:text-muted-foreground"
              )}
            >
              <Plug size={18} />
              Relationship
            </button>
            <button
              onClick={() => setView("dependencies")}
              className={cn(
                "inline-flex gap-3 items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
                view === "dependencies"
                  ? "bg-primary text-primary-foreground shadow-sm"
                  : "hover:bg-muted hover:text-muted-foreground"
              )}
            >
              <GitBranch size={18} />
              Dependencies
            </button>
            <button
              onClick={() => setView("activity")}
              className={cn(
                "inline-flex gap-3 items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
                view === "activity"
                  ? "bg-primary text-primary-foreground shadow-sm"
                  : "hover:bg-muted hover:text-muted-foreground"
              )}
            >
              <Clock size={18} />
              Activity
            </button>
          </div>
        </ScrollArea>
      </div>

      {view === "prd" ? (
        <>
          <Room id={id}>
            <div>
              <Editor />
              <div className="flex items-center gap-2 mt-10 mb-4">
                <Inbox size={18} />
                <h6>Comments</h6>
              </div>
              <Comments roomId={`feature:${id}`} />
            </div>
          </Room>
        </>
      ) : null}

      {view === "details" ? (
        <div className="py-6">
          {featureHierarchy?.parentFeature && (
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                <Plug className="h-4 w-4" />
                Parent Feature
              </h3>
              <Link
                href={`/features/${featureHierarchy.parentFeature.id}`}
                className="block p-3 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                  <div>
                    <h4 className="font-medium">
                      {featureHierarchy.parentFeature.name}
                    </h4>
                    {featureHierarchy.parentFeature.description && (
                      <p className="text-sm text-muted-foreground mt-1">
                        {featureHierarchy.parentFeature.description}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <PhaseSelector
                      disabled
                      phase={featureHierarchy.parentFeature.phase}
                    />
                    <PrioritySelector
                      priority={featureHierarchy.parentFeature.priority}
                      disabled
                    />
                  </div>
                </div>
              </Link>
            </div>
          )}

          {(!featureHierarchy?.subFeatures ||
            featureHierarchy.subFeatures.length === 0) && (
            <div className="mt-6">
              <NewFeature
                projectId={feature.projectId}
                parentFeatureId={feature.id}
              />
            </div>
          )}

          {/* Sub-Features Section */}
          {featureHierarchy?.subFeatures &&
            featureHierarchy.subFeatures.length > 0 && (
              <div className="space-y-3 mt-6">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                  <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                    <Plug className="h-4 w-4" />
                    Sub-Features ({featureHierarchy.subFeatures.length})
                  </h3>
                  <NewFeature
                    projectId={feature.projectId}
                    parentFeatureId={feature.id}
                  />
                </div>
                <div className="space-y-2">
                  {featureHierarchy.subFeatures.map((subFeature: any) => (
                    <Link
                      key={subFeature.id}
                      href={`/features/${subFeature.id}`}
                      className="block p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                        <div className="space-y-1">
                          <h4 className="font-medium">{subFeature.name}</h4>
                          {subFeature.description && (
                            <p className="text-sm text-muted-foreground">
                              {subFeature.description}
                            </p>
                          )}
                        </div>
                        <div className="flex flex-wrap items-center gap-2">
                          <PhaseSelector disabled phase={subFeature.phase} />
                          <PrioritySelector
                            priority={subFeature.priority}
                            disabled
                          />
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            )}
        </div>
      ) : null}

      {view === "dependencies" ? (
        <div className="py-6">
          <FeatureDependencyManager
            featureId={id as string}
            projectId={feature.projectId}
          />
        </div>
      ) : null}

      {view === "activity" ? (
        <div className="py-6">
          <ActivityFeed
            entityType="FEATURE"
            entityId={id}
            emptyMessage="No feature activity yet"
          />
        </div>
      ) : null}
    </div>
  );
};

export default FeatureDetails;
