import { createAuthClient } from "better-auth/react";
import {
  organizationClient,
  passkeyClient,
  twoFactorClient,
} from "better-auth/client/plugins";
import { polarClient } from "@polar-sh/better-auth";
// @ts-ignore
import { convexClient } from "@convex-dev/better-auth/client/plugins";

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_AUTH_URL,
  plugins: [
    passkeyClient(),
    organizationClient(),
    twoFactorClient(),
    polarClient(),
    convexClient(),
  ],
});
