import { v } from "convex/values";
import { mutation, query, action } from "../_generated/server";
import { betterAuth } from "../auth";
import { ConvexError } from "convex/values";

// QUERIES

// Get GitHub repositories for an integration
export const getGitHubRepositories = query({
  args: { integrationId: v.id("Integration") },
  handler: async (ctx, { integrationId }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    // Verify integration belongs to user's organization
    const integration = await ctx.db.get(integrationId);
    if (!integration || integration.organizationId !== auth.organizationId) {
      throw new ConvexError("Integration not found or access denied");
    }

    // Get GitHub repositories from integration config
    const config = integration.config as any;
    return config?.repositories || [];
  },
});

// MUTATIONS

// Update GitHub repositories for an integration
export const updateGitHubRepositories = mutation({
  args: {
    integrationId: v.id("Integration"),
    repositories: v.array(v.object({
      id: v.number(),
      name: v.string(),
      fullName: v.string(),
      private: v.boolean(),
      enabled: v.boolean(),
    })),
  },
  handler: async (ctx, { integrationId, repositories }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    // Verify integration belongs to user's organization
    const integration = await ctx.db.get(integrationId);
    if (!integration || integration.organizationId !== auth.organizationId) {
      throw new ConvexError("Integration not found or access denied");
    }

    // Update integration config with repositories
    const currentConfig = integration.config as any || {};
    const updatedConfig = {
      ...currentConfig,
      repositories,
    };

    await ctx.db.patch(integrationId, {
      config: updatedConfig,
    });

    return true;
  },
});

// ACTIONS

// Fetch repositories from GitHub API
export const fetchGitHubRepositories = action({
  args: { integrationId: v.id("Integration") },
  handler: async (ctx, { integrationId }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    // Get integration
    const integration = await ctx.runQuery("settings.integration.getIntegration", {
      id: integrationId,
    });

    if (!integration) {
      throw new ConvexError("Integration not found");
    }

    const config = integration.config as any;
    const accessToken = config?.accessToken;

    if (!accessToken) {
      throw new ConvexError("GitHub access token not found");
    }

    try {
      // Fetch repositories from GitHub API
      const response = await fetch("https://api.github.com/user/repos?per_page=100", {
        headers: {
          Authorization: `token ${accessToken}`,
          Accept: "application/vnd.github.v3+json",
        },
      });

      if (!response.ok) {
        throw new Error(`GitHub API error: ${response.status}`);
      }

      const repos = await response.json();

      // Transform to our format
      const repositories = repos.map((repo: any) => ({
        id: repo.id,
        name: repo.name,
        fullName: repo.full_name,
        private: repo.private,
        enabled: false, // Default to disabled
      }));

      return repositories;
    } catch (error) {
      console.error("Failed to fetch GitHub repositories:", error);
      throw new ConvexError("Failed to fetch repositories from GitHub");
    }
  },
});
