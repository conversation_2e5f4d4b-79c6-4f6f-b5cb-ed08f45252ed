"use client";

import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { But<PERSON> } from "@workspace/ui/components/button";
import Link from "next/link";
import LoadingSpinner from "@workspace/ui/components/loading-spinner";
import { ExpandedLayoutContainer } from "@/components/expanded-layout-container";
import { ProjectSidebar } from "./project-sidebar";
import { ProjectContent } from "./project-content";
import { ProjectLayoutWrapper } from "./project-layout-wrapper";
import { ErrorBoundary } from "./error-boundary";
import { ReactNode } from "react";
import Header from "@/components/shared/header";

interface ProjectLayoutClientProps {
  projectId: string;
  children: ReactNode;
}

export function ProjectLayoutClient({ projectId, children }: ProjectLayoutClientProps) {
  const project = useQuery(api.projects.index.getProject, {
    id: projectId as Id<"Project">,
  });

  // Handle loading state
  if (project === undefined) {
    return <LoadingSpinner />;
  }

  // Handle not found
  if (project === null) {
    return (
      <div className="flex-1 flex items-center mt-10 justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-3">Project not found</h1>
          <p className="text-muted-foreground max-w-md">
            We couldn't find the project you're looking for. It may have
            been deleted or you may not have access.
          </p>
          <Button asChild className="mt-4">
            <Link href="/project">Back to Projects</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <Header
        crumb={[
          {
            title: "Projects",
            url: "/project",
          },
          {
            title: project?.name || "",
            url: `/project/${project?.id}`,
          },
        ]}
      >
        {null}
      </Header>
      <ExpandedLayoutContainer>
        <ProjectLayoutWrapper>
          <ProjectSidebar projectId={projectId} />
          <ProjectContent>
            {children}
          </ProjectContent>
        </ProjectLayoutWrapper>
      </ExpandedLayoutContainer>
    </ErrorBoundary>
  );
}
