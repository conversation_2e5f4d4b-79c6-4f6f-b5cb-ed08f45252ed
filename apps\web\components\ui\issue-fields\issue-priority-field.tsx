"use client";

import { IssueFieldBase } from "./issue-field-base";
import { priorities } from "@/utils/constants/issues/priority";
import { IssuePriorityBadge } from "@/components/project/issues/issue-badge";
import { useMutation } from "convex/react";
import { api, Id } from "@workspace/backend";

interface IssuePriorityFieldProps {
  issueId: string;
  value: string;
  className?: string;
  disabled?: boolean;
  align?: "start" | "center" | "end";
  onChange?: (priority: string) => Promise<void>;
}

export function IssuePriorityField({
  issueId,
  value,
  disabled,
  align,
  onChange,
}: IssuePriorityFieldProps) {
  const updateIssue = useMutation(api.issues.index.updateIssue);

  return (
    <IssueFieldBase
      value={value}
      onSave={async (newValue: any) => {
        if (onChange) {
          await onChange(newValue);
        } else {
          await updateIssue({ id: issueId as Id<"Issue">, priority: newValue });
        }
      }}
      options={priorities.map((p) => ({
        id: p.id,
        name: p.name,
        icon: p.icon,
      }))}
      displayValue={<IssuePriorityBadge priority={value} />}
      placeholder="Set priority"
      searchPlaceholder="Search priority..."
      emptyText="No priority found."
      disabled={disabled}
      errorMessage="Failed to update priority"
      align={align}
      className="w-fit"
    />
  );
}
