"use client";

import { use<PERSON>em<PERSON>, useState } from "react";
import { usePaginatedQuery, useMutation, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Textarea } from "@workspace/ui/components/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@workspace/ui/components/avatar";
import { Separator } from "@workspace/ui/components/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@workspace/ui/components/tooltip";
import { ScrollArea } from "@workspace/ui/components/scroll-area";
import { Popover, PopoverContent, PopoverTrigger } from "@workspace/ui/components/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@workspace/ui/components/command";
import { <PERSON><PERSON><PERSON>, SmilePlus, <PERSON>ader2, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react";
import { cn } from "@/lib/utils";
import { useSession } from "@/context/session-context";

type Member = { id: string; user: { name: string; image?: string; email: string } };

export function Comments({ roomId }: { roomId: string }) {
  const { userId } = useSession();
  const threads = usePaginatedQuery(api.comments.listThreads, { roomId }, { initialNumItems: 50 });
  const subscribe = useMutation(api.comments.subscribe);
  const unsubscribe = useMutation(api.comments.unsubscribe);
  const isSubscribed = useQuery(api.comments.unreadCount, {}); // placeholder for badge elsewhere

  const [composer, setComposer] = useState("");
  const [openMention, setOpenMention] = useState(false);
  const orgMembers = useQuery(api.auth.getOrgMembers) as Member[] | undefined;
  const createThread = useMutation(api.comments.createThread);

  const [selectedMentions, setSelectedMentions] = useState<string[]>([]);

  const handleCreate = async () => {
    if (!composer.trim()) return;
    await createThread({ roomId, text: composer, mentions: selectedMentions });
    setComposer("");
    setSelectedMentions([]);
  };

  return (
    <div className="flex flex-col gap-3">
      <div className="rounded-md border p-2 bg-background">
        <Textarea
          value={composer}
          onChange={(e) => setComposer(e.target.value)}
          placeholder="Start a thread... Use @ to mention"
          rows={3}
        />
        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center gap-2">
            <MentionPicker
              members={orgMembers || []}
              onSelect={(id) => setSelectedMentions((prev) => Array.from(new Set([...prev, id])))}
            />
            <EmojiPicker onSelect={() => {}} />
            <AttachmentButton onFilesSelected={() => {}} />
          </div>
          <Button size="sm" onClick={handleCreate}>
            Post
          </Button>
        </div>
      </div>

      <Separator />

      <ScrollArea className="h-[calc(100vh-180px)] pr-2">
        <div className="space-y-3">
          {threads.results.map((t) => (
            <ThreadItem key={t._id} thread={t} roomId={roomId} />
          ))}
          {!threads.isDone && (
            <div className="flex justify-center">
              <Button variant="ghost" size="sm" onClick={() => threads.loadMore(50)}>
                Load more
              </Button>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
}

function ThreadItem({ thread, roomId }: { thread: any; roomId: string }) {
  const comments = usePaginatedQuery(api.comments.listThreadComments, { threadId: thread._id }, { initialNumItems: 20 });
  const resolveThread = useMutation(api.comments.resolveThread);
  const reply = useMutation(api.comments.reply);
  const { userId } = useSession();
  const orgMembers = useQuery(api.auth.getOrgMembers) as Member[] | undefined;
  const [text, setText] = useState("");
  const [mentions, setMentions] = useState<string[]>([]);

  return (
    <div className="rounded-md border p-3 bg-background">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Hash size={14} />
          <span>Thread</span>
        </div>
        <Button
          size="xs"
          variant={thread.resolved ? "secondary" : "outline"}
          onClick={() => resolveThread({ threadId: thread._id, resolved: !thread.resolved })}
        >
          {thread.resolved ? "Reopen" : "Resolve"}
        </Button>
      </div>

      <div className="mt-3 space-y-3">
        {comments.results.map((c) => (
          <CommentItem key={c._id} comment={c} />
        ))}
        {!comments.isDone && (
          <Button variant="ghost" size="sm" onClick={() => comments.loadMore(20)}>
            Load more
          </Button>
        )}
      </div>

      <div className="mt-3 rounded-md border p-2">
        <Textarea
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="Reply..."
          rows={2}
        />
        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center gap-2">
            <MentionPicker
              members={orgMembers || []}
              onSelect={(id) => setMentions((prev) => Array.from(new Set([...prev, id])))}
            />
            <EmojiPicker onSelect={() => {}} />
            <AttachmentButton onFilesSelected={() => {}} />
          </div>
          <Button
            size="sm"
            onClick={async () => {
              if (!text.trim()) return;
              await reply({ threadId: thread._id, text, mentions });
              setText("");
              setMentions([]);
            }}
          >
            <Reply className="mr-1" size={14} /> Reply
          </Button>
        </div>
      </div>
    </div>
  );
}

function CommentItem({ comment }: { comment: any }) {
  const { userId } = useSession();
  const react = useMutation(api.comments.react);
  const edit = useMutation(api.comments.edit);
  const del = useMutation(api.comments.del);
  const members = useQuery(api.auth.getOrgMembers) as Member[] | undefined;
  const author = useMemo(() => members?.find((m) => m.id === comment.authorId), [members, comment.authorId]);

  return (
    <div className="flex gap-2">
      <Avatar className="size-6">
        <AvatarImage src={author?.user.image || ""} />
        <AvatarFallback>{author?.user.name?.[0] || "?"}</AvatarFallback>
      </Avatar>
      <div className="flex-1">
        <div className="text-sm">
          <span className="font-medium">{author?.user.name || "User"}</span>
          <span className="text-muted-foreground ml-2">{new Date(comment.createdAt).toLocaleString()}</span>
        </div>
        <div className="mt-1 text-sm whitespace-pre-wrap">{comment.text}</div>
        <div className="mt-2 flex items-center gap-2">
          <Button variant="ghost" size="xs" onClick={() => react({ commentId: comment._id, emoji: "👍" })}>
            👍
          </Button>
          {comment.authorId === userId && (
            <>
              <Button variant="ghost" size="xs" onClick={() => del({ commentId: comment._id })}>
                Delete
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

function MentionPicker({ members, onSelect }: { members: Member[]; onSelect: (id: string) => void }) {
  const [open, setOpen] = useState(false);
  const [q, setQ] = useState("");
  const filtered = useMemo(() => {
    if (!q) return members;
    return members.filter((m) => m.user.name.toLowerCase().includes(q.toLowerCase()) || m.user.email.toLowerCase().includes(q.toLowerCase()));
  }, [q, members]);
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon-sm" aria-label="Mention">
          @
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-0 w-64">
        <Command>
          <CommandInput placeholder="Mention..." value={q} onValueChange={setQ} />
          <CommandList>
            <CommandEmpty>No users</CommandEmpty>
            <CommandGroup>
              {filtered.map((m) => (
                <CommandItem key={m.id} value={m.id} onSelect={() => (onSelect(m.id), setOpen(false))}>
                  <div className="flex items-center gap-2">
                    <Avatar className="size-5">
                      <AvatarImage src={m.user.image || ""} />
                      <AvatarFallback>{m.user.name?.[0] || "?"}</AvatarFallback>
                    </Avatar>
                    <div className="flex flex-col">
                      <span className="text-sm">{m.user.name}</span>
                      <span className="text-xs text-muted-foreground">{m.user.email}</span>
                    </div>
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

function EmojiPicker({ onSelect }: { onSelect: (emoji: string) => void }) {
  return (
    <Button variant="ghost" size="icon-sm" aria-label="Emoji">
      <SmilePlus size={16} />
    </Button>
  );
}

function AttachmentButton({ onFilesSelected }: { onFilesSelected: (files: FileList) => void }) {
  return (
    <label className="cursor-pointer inline-flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground">
      <Paperclip size={16} />
      <input type="file" multiple className="hidden" onChange={(e) => e.target.files && onFilesSelected(e.target.files)} />
    </label>
  );
}


