"use client";
import { But<PERSON> } from "@workspace/ui/components/button";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { toast } from "sonner";
import { Loader2, UserPlus, XCircle } from "lucide-react";
import { useAction } from "convex/react";
import { api } from "@workspace/backend";

interface JoinProps {
  id: string;
  organizationName: string;
}

const Join = ({ id, organizationName }: JoinProps) => {
  //

  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [rejecting, setRejecting] = useState(false);
  const acceptInvitation = useAction(api.auth.acceptInvitation);
  const rejectInvitation = useAction(api.auth.rejectInvitation);

  const handleSubmit = async () => {
    setLoading(true);
    try {
      const res = await acceptInvitation({ id });
      if (!res) {
        toast.error("Failed to accept invitation");
      }
      toast.success(`Successfully joined ${organizationName}!`);
      router.replace("/switch-org");
    } catch (error) {
      toast.error("Failed to accept invitation");
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  const handleReject = async () => {
    setRejecting(true);
    try {
      const res = await rejectInvitation({ id });
      if (!res) {
        toast.error("Failed to reject invitation");
      }
      toast.success(`Successfully rejected ${organizationName}!`);

      router.replace("/switch-org");
    } catch (error) {
      toast.error("Failed to reject invitation");
      setRejecting(false);
    } finally {
      setRejecting(false);
    }
  };

  return (
    <div className="space-y-4">
      <Button
        disabled={loading || rejecting}
        className="w-full"
        onClick={handleSubmit}
        size="lg"
      >
        {loading ? (
          <>
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            Joining Organization...
          </>
        ) : (
          <>
            <UserPlus className="w-4 h-4 mr-2" />
            Join {organizationName}
          </>
        )}
      </Button>

      <Button
        disabled={loading || rejecting}
        variant="outline"
        className="w-full"
        onClick={handleReject}
        size="lg"
      >
        {rejecting ? (
          <>
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            Rejecting...
          </>
        ) : (
          <>
            <XCircle className="w-4 h-4 mr-2" />
            Reject Invitation
          </>
        )}
      </Button>

      {(loading || rejecting) && (
        <p className="text-sm text-center text-muted-foreground">
          This may take a few moments...
        </p>
      )}
    </div>
  );
};

export default Join;
