import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { betterAuth } from "../auth";
import { ConvexError } from "convex/values";
import { IssueStatus, IssueLabel, Importance } from "../../schema/enum";

export const getRoadmapsByProject = query({
  args: { id: v.id("Project") },
  handler: async (ctx, { id }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) return null;

    const roadmaps = await ctx.db
      .query("PublicRoadmap")
      .withIndex("byProject", (q) => q.eq("projectId", id))
      .collect();

    return roadmaps;
  },
});

export const getRoadmapBySlug = query({
  args: { slug: v.string() },
  handler: async (ctx, { slug }) => {
    const roadmap = await ctx.db
      .query("PublicRoadmap")
      .withIndex("bySlug", (q) => q.eq("slug", slug))
      .unique();

    return roadmap;
  },
});

export const createRoadmap = mutation({
  args: {
    projectId: v.id("Project"),
    name: v.string(),
    slug: v.string(),
    description: v.string(),
    allowFeedback: v.boolean(),
    allowVoting: v.boolean(),
    isPublic: v.boolean(),
  },
  handler: async (
    ctx,
    { projectId, name, slug, description, allowFeedback, allowVoting, isPublic }
  ) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Unauthorized");

    const roadmapId = await ctx.db.insert("PublicRoadmap", {
      projectId,
      name,
      slug,
      description,
      allowFeedback,
      allowVoting,
      isPublic,
    });

    return roadmapId;
  },
});

export const updateRoadmap = mutation({
  args: {
    id: v.id("PublicRoadmap"),
    name: v.optional(v.string()),
    slug: v.optional(v.string()),
    description: v.optional(v.string()),
  },
  handler: async (ctx, { id, name, slug, description }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Unauthorized");

    const existingRoadmap = await ctx.db.get(id);
    if (!existingRoadmap) throw new Error("Roadmap not found");

    await ctx.db.patch(id, {
      ...(name && { name }),
      ...(slug && { slug }),
      ...(description && { description }),
    });

    return true;
  },
});

export const getRoadmapById = query({
  args: { id: v.id("PublicRoadmap") },
  handler: async (ctx, { id }) => {
    const roadmap = await ctx.db.get(id);
    return roadmap;
  },
});

// Get all roadmaps for the user's organization with enriched stats
export const getAllRoadmapsByOrg = query({
  args: {},
  handler: async (ctx) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Unauthorized");

    // Get all projects for the organization
    const projects = await ctx.db
      .query("Project")
      .filter((q) => q.eq(q.field("organizationId"), auth.organizationId))
      .collect();

    const projectIds = projects.map((p) => p._id);

    // Get all roadmaps for these projects
    const roadmaps = await ctx.db
      .query("PublicRoadmap")
      .filter((q) =>
        q.or(
          ...projectIds.map(projectId =>
            q.eq(q.field("projectId"), projectId)
          )
        )
      )
      .collect();

    // Enrich roadmaps with stats
    const enrichedRoadmaps = await Promise.all(
      roadmaps.map(async (roadmap) => {
        // Get project info
        const project = projects.find(p => p._id === roadmap.projectId);

        // Get roadmap items
        const items = await ctx.db
          .query("RoadmapItem")
          .filter((q) => q.eq(q.field("roadmapId"), roadmap._id))
          .collect();

        // Get changelogs count
        const changelogsCount = await ctx.db
          .query("RoadmapChangelog")
          .filter((q) => q.eq(q.field("roadmapId"), roadmap._id))
          .collect();

        // Get feature requests count
        const featureRequestsCount = await ctx.db
          .query("FeatureRequest")
          .filter((q) => q.eq(q.field("roadmapId"), roadmap._id))
          .collect();

        // Calculate vote and feedback counts for all items
        let totalVotes = 0;
        let totalFeedback = 0;
        const enrichedItems = await Promise.all(
          items.map(async (item) => {
            const votes = await ctx.db
              .query("RoadmapVote")
              .filter((q) => q.eq(q.field("roadmapItemId"), item._id))
              .collect();

            const feedback = await ctx.db
              .query("RoadmapFeedback")
              .filter((q) => q.eq(q.field("roadmapItemId"), item._id))
              .collect();

            totalVotes += votes.length;
            totalFeedback += feedback.length;

            return {
              ...item,
              voteCount: votes.length,
              feedbackCount: feedback.length,
            };
          })
        );

        return {
          ...roadmap,
          project: project ? {
            id: project._id,
            name: project.name,
            status: project.status,
            description: project.description,
            platform: project.platform,
            createdAt: project._creationTime,
          } : null,
          items: enrichedItems,
          stats: {
            totalItems: items.length,
            totalChangelogs: changelogsCount.length,
            totalFeatureRequests: featureRequestsCount.length,
            totalVotes,
            totalFeedback,
            lastUpdated: roadmap._creationTime,
          },
        };
      })
    );

    return enrichedRoadmaps;
  },
});

// Delete a roadmap
export const deleteRoadmap = mutation({
  args: { id: v.id("PublicRoadmap") },
  handler: async (ctx, { id }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Unauthorized");

    const roadmap = await ctx.db.get(id);
    if (!roadmap) throw new Error("Roadmap not found");

    // Verify the roadmap belongs to the user's organization
    const project = await ctx.db.get(roadmap.projectId);
    if (!project || project.organizationId !== auth.organizationId) {
      throw new Error("Unauthorized to delete this roadmap");
    }

    // Delete all related data
    // Delete roadmap items
    const items = await ctx.db
      .query("RoadmapItem")
      .filter((q) => q.eq(q.field("roadmapId"), id))
      .collect();

    for (const item of items) {
      // Delete votes for this item
      const votes = await ctx.db
        .query("RoadmapVote")
        .filter((q) => q.eq(q.field("roadmapItemId"), item._id))
        .collect();
      for (const vote of votes) {
        await ctx.db.delete(vote._id);
      }

      // Delete feedback for this item
      const feedback = await ctx.db
        .query("RoadmapFeedback")
        .filter((q) => q.eq(q.field("roadmapItemId"), item._id))
        .collect();
      for (const fb of feedback) {
        await ctx.db.delete(fb._id);
      }

      // Delete the item
      await ctx.db.delete(item._id);
    }

    // Delete changelogs
    const changelogs = await ctx.db
      .query("RoadmapChangelog")
      .filter((q) => q.eq(q.field("roadmapId"), id))
      .collect();
    for (const changelog of changelogs) {
      await ctx.db.delete(changelog._id);
    }

    // Delete feature requests
    const featureRequests = await ctx.db
      .query("FeatureRequest")
      .filter((q) => q.eq(q.field("roadmapId"), id))
      .collect();
    for (const request of featureRequests) {
      await ctx.db.delete(request._id);
    }

    // Finally delete the roadmap
    await ctx.db.delete(id);

    return true;
  },
});

// ROADMAP ITEMS

// Get all roadmap items for a roadmap
export const getRoadmapItems = query({
  args: { roadmapId: v.id("PublicRoadmap") },
  handler: async (ctx, { roadmapId }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    // Verify roadmap belongs to user's organization
    const roadmap = await ctx.db.get(roadmapId);
    if (!roadmap) throw new ConvexError("Roadmap not found");

    const project = await ctx.db.get(roadmap.projectId);
    if (!project || project.organizationId !== auth.organizationId) {
      throw new ConvexError("Access denied");
    }

    const items = await ctx.db
      .query("RoadmapItem")
      .filter((q) => q.eq(q.field("roadmapId"), roadmapId))
      .collect();

    // Enrich with vote and feedback counts
    const enrichedItems = await Promise.all(
      items.map(async (item) => {
        const votes = await ctx.db
          .query("RoadmapVote")
          .filter((q) => q.eq(q.field("roadmapItemId"), item._id))
          .collect();

        const feedback = await ctx.db
          .query("RoadmapFeedback")
          .filter((q) => q.eq(q.field("roadmapItemId"), item._id))
          .collect();

        return {
          ...item,
          voteCount: votes.length,
          feedbackCount: feedback.length,
        };
      })
    );

    return enrichedItems.sort((a, b) => (b.priority || 0) - (a.priority || 0));
  },
});

// Create a new roadmap item
export const createRoadmapItem = mutation({
  args: {
    roadmapId: v.id("PublicRoadmap"),
    title: v.string(),
    description: v.string(),
    status: IssueStatus,
    priority: Importance,
    targetDate: v.optional(v.number()),
    category: IssueLabel,
    isPublic: v.optional(v.boolean()),
  },
  handler: async (ctx, { roadmapId, title, description, status, priority, targetDate, category, isPublic }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    // Verify roadmap belongs to user's organization
    const roadmap = await ctx.db.get(roadmapId);
    if (!roadmap) throw new ConvexError("Roadmap not found");

    const project = await ctx.db.get(roadmap.projectId);
    if (!project || project.organizationId !== auth.organizationId) {
      throw new ConvexError("Access denied");
    }

    const itemId = await ctx.db.insert("RoadmapItem", {
      roadmapId,
      title,
      description,
      status,
      priority,
      targetDate,
      category,
      isPublic: isPublic ?? true,
    });

    return itemId;
  },
});

// Update a roadmap item
export const updateRoadmapItem = mutation({
  args: {
    id: v.id("RoadmapItem"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    status: v.optional(IssueStatus),
    priority: v.optional(Importance),
    targetDate: v.optional(v.number()),
    category: v.optional(IssueLabel),
    isPublic: v.optional(v.boolean()),
  },
  handler: async (ctx, { id, title, description, status, priority, targetDate, category, isPublic }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    const item = await ctx.db.get(id);
    if (!item) throw new ConvexError("Roadmap item not found");

    // Verify roadmap belongs to user's organization
    const roadmap = await ctx.db.get(item.roadmapId);
    if (!roadmap) throw new ConvexError("Roadmap not found");

    const project = await ctx.db.get(roadmap.projectId);
    if (!project || project.organizationId !== auth.organizationId) {
      throw new ConvexError("Access denied");
    }

    const updates: any = {};
    if (title !== undefined) updates.title = title;
    if (description !== undefined) updates.description = description;
    if (status !== undefined) updates.status = status;
    if (priority !== undefined) updates.priority = priority;
    if (targetDate !== undefined) updates.targetDate = targetDate;
    if (category !== undefined) updates.category = category;
    if (isPublic !== undefined) updates.isPublic = isPublic;

    await ctx.db.patch(id, updates);
    return true;
  },
});

// Delete a roadmap item
export const deleteRoadmapItem = mutation({
  args: { id: v.id("RoadmapItem") },
  handler: async (ctx, { id }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    const item = await ctx.db.get(id);
    if (!item) throw new ConvexError("Roadmap item not found");

    // Verify roadmap belongs to user's organization
    const roadmap = await ctx.db.get(item.roadmapId);
    if (!roadmap) throw new ConvexError("Roadmap not found");

    const project = await ctx.db.get(roadmap.projectId);
    if (!project || project.organizationId !== auth.organizationId) {
      throw new ConvexError("Access denied");
    }

    // Delete related votes and feedback
    const votes = await ctx.db
      .query("RoadmapVote")
      .filter((q) => q.eq(q.field("roadmapItemId"), id))
      .collect();
    for (const vote of votes) {
      await ctx.db.delete(vote._id);
    }

    const feedback = await ctx.db
      .query("RoadmapFeedback")
      .filter((q) => q.eq(q.field("roadmapItemId"), id))
      .collect();
    for (const fb of feedback) {
      await ctx.db.delete(fb._id);
    }

    await ctx.db.delete(id);
    return true;
  },
});

// CHANGELOG FUNCTIONS

// Update a roadmap changelog
export const updateRoadmapChangelog = mutation({
  args: {
    id: v.id("RoadmapChangelog"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    version: v.optional(v.string()),
    publishDate: v.optional(v.number()),
    isPublished: v.optional(v.boolean()),
  },
  handler: async (ctx, { id, title, description, version, publishDate, isPublished }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    const changelog = await ctx.db.get(id);
    if (!changelog) throw new ConvexError("Changelog not found");

    // Verify changelog belongs to user's organization
    const roadmap = await ctx.db.get(changelog.roadmapId);
    if (!roadmap) throw new ConvexError("Roadmap not found");

    const project = await ctx.db.get(roadmap.projectId);
    if (!project || project.organizationId !== auth.organizationId) {
      throw new ConvexError("Access denied");
    }

    const updates: any = {};
    if (title !== undefined) updates.title = title;
    if (description !== undefined) updates.description = description;
    if (version !== undefined) updates.version = version;
    if (publishDate !== undefined) updates.publishDate = publishDate;
    if (isPublished !== undefined) updates.isPublished = isPublished;

    await ctx.db.patch(id, updates);
    return true;
  },
});

// Delete a roadmap changelog
export const deleteRoadmapChangelog = mutation({
  args: { id: v.id("RoadmapChangelog") },
  handler: async (ctx, { id }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    const changelog = await ctx.db.get(id);
    if (!changelog) throw new ConvexError("Changelog not found");

    // Verify changelog belongs to user's organization
    const roadmap = await ctx.db.get(changelog.roadmapId);
    if (!roadmap) throw new ConvexError("Roadmap not found");

    const project = await ctx.db.get(roadmap.projectId);
    if (!project || project.organizationId !== auth.organizationId) {
      throw new ConvexError("Access denied");
    }

    // Delete related entries
    const entries = await ctx.db
      .query("ChangelogEntry")
      .filter((q) => q.eq(q.field("changelogId"), id))
      .collect();

    for (const entry of entries) {
      await ctx.db.delete(entry._id);
    }

    await ctx.db.delete(id);
    return true;
  },
});

// Delete a changelog entry
export const deleteChangelogEntry = mutation({
  args: { id: v.id("ChangelogEntry") },
  handler: async (ctx, { id }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    const entry = await ctx.db.get(id);
    if (!entry) throw new ConvexError("Changelog entry not found");

    // Verify entry belongs to user's organization
    const changelog = await ctx.db.get(entry.changelogId);
    if (!changelog) throw new ConvexError("Changelog not found");

    const roadmap = await ctx.db.get(changelog.roadmapId);
    if (!roadmap) throw new ConvexError("Roadmap not found");

    const project = await ctx.db.get(roadmap.projectId);
    if (!project || project.organizationId !== auth.organizationId) {
      throw new ConvexError("Access denied");
    }

    await ctx.db.delete(id);
    return true;
  },
});

// Get available items for changelog
export const getAvailableItemsForChangelog = query({
  args: { roadmapId: v.id("PublicRoadmap") },
  handler: async (ctx, { roadmapId }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    // Verify roadmap belongs to user's organization
    const roadmap = await ctx.db.get(roadmapId);
    if (!roadmap) throw new ConvexError("Roadmap not found");

    const project = await ctx.db.get(roadmap.projectId);
    if (!project || project.organizationId !== auth.organizationId) {
      throw new ConvexError("Access denied");
    }

    // Get issues and features for the project
    const issues = await ctx.db
      .query("Issue")
      .filter((q) => q.eq(q.field("projectId"), roadmap.projectId))
      .collect();

    const features = await ctx.db
      .query("Feature")
      .filter((q) => q.eq(q.field("projectId"), roadmap.projectId))
      .collect();

    return {
      issues,
      features,
    };
  },
});
