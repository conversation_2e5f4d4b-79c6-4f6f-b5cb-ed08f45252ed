"use server";

import { ConvexHttpClient } from "convex/browser";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { getSession } from "../account/user";

const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

export const prepValidation = async ({ ideaId }: { ideaId: string }) => {
  await getSession();

  // Use Convex action instead of Inngest
  await convex.action(api.actions.ideaValidation.prepValidation, {
    ideaId: ideaId as Id<"Idea">,
  });

  return { success: true };
};

export const findCompetitors = async ({ ideaId }: { ideaId: string }) => {
  await getSession();

  // Use Convex action instead of Inngest
  await convex.action(api.actions.ideaValidation.findCompetitors, {
    ideaId: ideaId as Id<"Idea">,
  });

  return { success: true };
};

export const findCompetitorMoves = async ({
  ideaId,
  competitorId,
}: {
  ideaId: string;
  competitorId: string;
}) => {
  await getSession();

  // Use Convex action instead of Inngest
  await convex.action(api.actions.ideaValidation.findCompetitorMoves, {
    ideaId: ideaId as Id<"Idea">,
    competitorId,
  });

  return { success: true };
};
