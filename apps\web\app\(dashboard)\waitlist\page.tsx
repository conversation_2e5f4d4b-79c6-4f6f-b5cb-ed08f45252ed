import WaitlistClient from "./waitlist-client";
import Header from "@/components/shared/header";
import Link from "next/link";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Plus } from "lucide-react";

export default function WaitlistPage() {
  return (
    <div>
      <Header crumb={[{ title: "Waitlists", url: "/waitlist" }]}>
        <Link href="/waitlist/new">
          <Button size="sm" variant="fancy">
            <Plus className="w-4 h-4 mr-2" /> New Waitlist
          </Button>
        </Link>
      </Header>
      <WaitlistClient />
    </div>
  );
}
