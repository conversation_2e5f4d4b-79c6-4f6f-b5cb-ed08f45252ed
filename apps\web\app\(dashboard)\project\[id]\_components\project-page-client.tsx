"use client";

import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { Badge } from "@workspace/ui/components/badge";
import { MilestoneMetrics } from "@/components/project/milestone-metrics";
import {
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Star,
  Bug,
  Target,
  Clock,
  User,
  Calendar,
  Activity,
} from "lucide-react";
import UpcomingDeadlines from "./upcomingDeadlines";

interface ProjectPageClientProps {
  projectId: string;
}

export function ProjectPageClient({ projectId }: ProjectPageClientProps) {
  const insights = useQuery(api.projects.index.getProjectInsights, {
    id: projectId as Id<"Project">,
  });

  if (insights === undefined) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (insights === null) {
    return <div>Project not found</div>;
  }

  return (
    <div className="space-y-6">
      {/* Project Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-card rounded-lg border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Total Issues
              </p>
              <p className="text-2xl font-bold">{insights.totalIssues || 0}</p>
            </div>
            <Bug className="h-8 w-8 text-muted-foreground" />
          </div>
        </div>

        <div className="bg-card rounded-lg border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Open Issues
              </p>
              <p className="text-2xl font-bold">{insights.openIssues || 0}</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-orange-500" />
          </div>
        </div>

        <div className="bg-card rounded-lg border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Completed
              </p>
              <p className="text-2xl font-bold">{insights.completedIssues || 0}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-card rounded-lg border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Features
              </p>
              <p className="text-2xl font-bold">{insights.totalFeatures || 0}</p>
            </div>
            <Star className="h-8 w-8 text-blue-500" />
          </div>
        </div>
      </div>

      {/* Milestone Metrics */}
      <MilestoneMetrics projectId={projectId} />

      {/* Recent Activity and Upcoming Deadlines */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <div className="bg-card rounded-lg border p-6">
          <div className="flex items-center gap-2 mb-4">
            <Activity className="h-5 w-5" />
            <h3 className="text-lg font-semibold">Recent Activity</h3>
          </div>
          <div className="space-y-3">
            {insights.recentActivity?.length > 0 ? (
              insights.recentActivity.map((activity: any, index: number) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-muted-foreground">
                      {activity.description}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(activity.timestamp).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-sm text-muted-foreground">No recent activity</p>
            )}
          </div>
        </div>

        {/* Upcoming Deadlines */}
        <UpcomingDeadlines projectId={projectId} />
      </div>

      {/* Project Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-card rounded-lg border p-4">
          <div className="flex items-center gap-2 mb-2">
            <Target className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium text-muted-foreground">
              Progress
            </span>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Completion</span>
              <span>{insights.completionPercentage || 0}%</span>
            </div>
            <div className="w-full bg-muted rounded-full h-2">
              <div
                className="bg-primary h-2 rounded-full transition-all"
                style={{ width: `${insights.completionPercentage || 0}%` }}
              />
            </div>
          </div>
        </div>

        <div className="bg-card rounded-lg border p-4">
          <div className="flex items-center gap-2 mb-2">
            <User className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium text-muted-foreground">
              Team
            </span>
          </div>
          <p className="text-2xl font-bold">{insights.teamMembers || 0}</p>
          <p className="text-xs text-muted-foreground">Active members</p>
        </div>

        <div className="bg-card rounded-lg border p-4">
          <div className="flex items-center gap-2 mb-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium text-muted-foreground">
              Timeline
            </span>
          </div>
          <p className="text-sm font-medium">
            {insights.nextDeadline
              ? new Date(insights.nextDeadline).toLocaleDateString()
              : "No upcoming deadlines"}
          </p>
          <p className="text-xs text-muted-foreground">Next milestone</p>
        </div>
      </div>
    </div>
  );
}
