import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { betterAuth } from "../auth";
import { ConvexError } from "convex/values";
import { IssueStatus, Importance, IssueLabel } from "../../schema/enum";

// QUERIES

// Get upcoming deadlines for a project
export const getUpcomingDeadlines = query({
  args: { projectId: v.id("Project") },
  handler: async (ctx, { projectId }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    // Verify project belongs to user's organization
    const project = await ctx.db.get(projectId);
    if (!project || project.organizationId !== auth.organizationId) {
      throw new ConvexError("Project not found or access denied");
    }

    // Get issues with upcoming deadlines (next 30 days)
    const thirtyDaysFromNow = Date.now() + (30 * 24 * 60 * 60 * 1000);
    
    const issues = await ctx.db
      .query("Issue")
      .filter((q) => q.eq(q.field("projectId"), projectId))
      .collect();

    // Filter issues with deadlines in the next 30 days
    const upcomingIssues = issues.filter(issue => 
      issue.dueDate && 
      issue.dueDate > Date.now() && 
      issue.dueDate <= thirtyDaysFromNow &&
      issue.status !== "DONE" &&
      issue.status !== "CANCELLED"
    );

    // Sort by due date
    upcomingIssues.sort((a, b) => (a.dueDate || 0) - (b.dueDate || 0));

    return upcomingIssues;
  },
});

// Get all issues for a project
export const getProjectIssues = query({
  args: { 
    projectId: v.id("Project"),
    status: v.optional(IssueStatus),
    assigneeId: v.optional(v.id("User")),
  },
  handler: async (ctx, { projectId, status, assigneeId }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    // Verify project belongs to user's organization
    const project = await ctx.db.get(projectId);
    if (!project || project.organizationId !== auth.organizationId) {
      throw new ConvexError("Project not found or access denied");
    }

    let query = ctx.db
      .query("Issue")
      .filter((q) => q.eq(q.field("projectId"), projectId));

    const issues = await query.collect();

    // Apply filters
    let filteredIssues = issues;

    if (status) {
      filteredIssues = filteredIssues.filter(issue => issue.status === status);
    }

    if (assigneeId) {
      filteredIssues = filteredIssues.filter(issue => issue.assignedToId === assigneeId);
    }

    return filteredIssues.sort((a, b) => b._creationTime - a._creationTime);
  },
});

// Get a single issue by ID
export const getIssue = query({
  args: { id: v.id("Issue") },
  handler: async (ctx, { id }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    const issue = await ctx.db.get(id);
    if (!issue) return null;

    // Verify issue belongs to user's organization
    const project = await ctx.db.get(issue.projectId);
    if (!project || project.organizationId !== auth.organizationId) {
      throw new ConvexError("Access denied");
    }

    return issue;
  },
});

// MUTATIONS

// Create a new issue
export const createIssue = mutation({
  args: {
    projectId: v.id("Project"),
    title: v.string(),
    description: v.optional(v.string()),
    status: IssueStatus,
    priority: v.optional(Importance),
    assigneeId: v.optional(v.id("User")),
    dueDate: v.optional(v.number()),
    label:IssueLabel,
  },
  handler: async (ctx, { projectId, title, description, status, priority, assigneeId, dueDate, label }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    // Verify project belongs to user's organization
    const project = await ctx.db.get(projectId);
    if (!project || project.organizationId !== auth.organizationId) {
      throw new ConvexError("Project not found or access denied");
    }

    const issueId = await ctx.db.insert("Issue", {
      projectId,
      title,
      description,
      status,
      priority: priority || "MEDIUM",
      assignedToId: assigneeId,
      dueDate,
      label,
      organizationId: auth.organizationId,
    });

    return issueId;
  },
});

// Update an issue
export const updateIssue = mutation({
  args: {
    id: v.id("Issue"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    status: v.optional(IssueStatus),
    priority: v.optional(Importance),
    assigneeId: v.optional(v.id("User")),
    dueDate: v.optional(v.number()),
    label: v.optional(IssueLabel),
  },
  handler: async (ctx, { id, title, description, status, priority, assigneeId, dueDate, label }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    const issue = await ctx.db.get(id);
    if (!issue) throw new ConvexError("Issue not found");

    // Verify issue belongs to user's organization
    const project = await ctx.db.get(issue.projectId);
    if (!project || project.organizationId !== auth.organizationId) {
      throw new ConvexError("Access denied");
    }

    const updates: any = {};
    if (title !== undefined) updates.title = title;
    if (description !== undefined) updates.description = description;
    if (status !== undefined) updates.status = status;
    if (priority !== undefined) updates.priority = priority;
    if (assigneeId !== undefined) updates.assignedToId = assigneeId;
    if (dueDate !== undefined) updates.dueDate = dueDate;
    if (label !== undefined) updates.label = label;

    await ctx.db.patch(id, updates);
    return true;
  },
});

// Delete an issue
export const deleteIssue = mutation({
  args: { id: v.id("Issue") },
  handler: async (ctx, { id }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    const issue = await ctx.db.get(id);
    if (!issue) throw new ConvexError("Issue not found");

    // Verify issue belongs to user's organization
    const project = await ctx.db.get(issue.projectId);
    if (!project || project.organizationId !== auth.organizationId) {
      throw new ConvexError("Access denied");
    }

    await ctx.db.delete(id);
    return true;
  },
});
