import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { betterAuth } from "../auth";

export const getRoadmapVotesByRoadmapItem = query({
  args: {
    roadmapItemId: v.id("RoadmapItem"),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    return await ctx.db
      .query("RoadmapVote")
      .filter((q) => q.eq(q.field("roadmapItemId"), args.roadmapItemId))
      .collect();
  },
});

export const createRoadmapVote = mutation({
  args: {
    roadmapItemId: v.id("RoadmapItem"),
    userId: v.optional(v.string()),
    ipAddress: v.string(),
  },
  handler: async (ctx, args) => {
    // No authentication for voting, as per typical public roadmap behavior
    return await ctx.db.insert("RoadmapVote", {
      roadmapItemId: args.roadmapItemId,
      userId: args.userId,
      ipAddress: args.ipAddress,
      createdAt: Date.now(),
    });
  },
});

export const deleteRoadmapVote = mutation({
  args: {
    id: v.id("RoadmapVote"),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    await ctx.db.delete(args.id);
    return { success: true };
  },
});
