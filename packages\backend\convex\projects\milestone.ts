import { v } from "convex/values";
import { query, mutation } from "../_generated/server";
import { betterAuth } from "../auth";
import { ConvexError } from "convex/values";
import { MilestoneStatus } from "../../schema/enum";

export const getMilestones = query({
  args: {
    id: v.id("Project"),
  },
  handler: async (ctx, { id }) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const milestones = await ctx.db
      .query("Milestone")
      .withIndex("by_project", (q) => q.eq("projectId", id))
      .collect();

    return milestones;
  },
});

// Get a single milestone by ID
export const getMilestone = query({
  args: { id: v.id("Milestone") },
  handler: async (ctx, { id }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    const milestone = await ctx.db.get(id);
    if (!milestone) return null;

    // Verify milestone belongs to user's organization
    const project = await ctx.db.get(milestone.projectId);
    if (!project || project.organizationId !== auth.organizationId) {
      throw new ConvexError("Access denied");
    }

    return milestone;
  },
});

// Create a new milestone
export const createMilestone = mutation({
  args: {
    projectId: v.id("Project"),
    name: v.string(),
    description: v.optional(v.string()),
    status: MilestoneStatus,
    progress: v.optional(v.number()),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
    ownerId: v.optional(v.id("User")),
  },
  handler: async (ctx, { projectId, name, description, status, progress, startDate, endDate, ownerId }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    // Verify project belongs to user's organization
    const project = await ctx.db.get(projectId);
    if (!project || project.organizationId !== auth.organizationId) {
      throw new ConvexError("Project not found or access denied");
    }

    const milestoneId = await ctx.db.insert("Milestone", {
      projectId,
      name,
      description,
      status,
      startDate,
      endDate,
      organizationId: auth.organizationId,
      ...(ownerId ? { ownerId } : {}),
    });

    return milestoneId;
  },
});

// Update a milestone
export const updateMilestone = mutation({
  args: {
    id: v.id("Milestone"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    status: v.optional(MilestoneStatus),
    progress: v.optional(v.number()),
    endDate: v.optional(v.number()),
  },
  handler: async (ctx, { id, name, description, status, progress, endDate }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    const milestone = await ctx.db.get(id);
    if (!milestone) throw new ConvexError("Milestone not found");

    // Verify milestone belongs to user's organization
    const project = await ctx.db.get(milestone.projectId);
    if (!project || project.organizationId !== auth.organizationId) {
      throw new ConvexError("Access denied");
    }

    const updates: any = {};
    if (name !== undefined) updates.name = name;
    if (description !== undefined) updates.description = description;
    if (status !== undefined) updates.status = status;
    if (progress !== undefined) updates.progress = progress;
    if (endDate !== undefined) updates.endDate = endDate;

    await ctx.db.patch(id, updates);
    return true;
  },
});

// Delete a milestone
export const deleteMilestone = mutation({
  args: { id: v.id("Milestone") },
  handler: async (ctx, { id }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    const milestone = await ctx.db.get(id);
    if (!milestone) throw new ConvexError("Milestone not found");

    // Verify milestone belongs to user's organization
    const project = await ctx.db.get(milestone.projectId);
    if (!project || project.organizationId !== auth.organizationId) {
      throw new ConvexError("Access denied");
    }

    await ctx.db.delete(id);
    return true;
  },
});

// Get milestone metrics for a project
export const getMilestoneMetrics = query({
  args: { projectId: v.id("Project") },
  handler: async (ctx, { projectId }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    // Verify project belongs to user's organization
    const project = await ctx.db.get(projectId);
    if (!project || project.organizationId !== auth.organizationId) {
      throw new ConvexError("Project not found or access denied");
    }

    const milestones = await ctx.db
      .query("Milestone")
      .filter((q) => q.eq(q.field("projectId"), projectId))
      .collect();

    // Calculate metrics
    const totalMilestones = milestones.length;
    const completedMilestones = milestones.filter(m => m.status === "COMPLETED").length;
    const inProgressMilestones = milestones.filter(m => m.status === "IN_PROGRESS").length;
    const overdueMilestones = milestones.filter(m =>
      m.endDate && m.endDate < Date.now() && m.status !== "COMPLETED"
    ).length;

    const completionRate = totalMilestones > 0 ? (completedMilestones / totalMilestones) * 100 : 0;

    // Calculate health status based on completion rate and overdue milestones
    let healthStatus = "excellent";
    if (overdueMilestones > 0 || completionRate < 50) {
      healthStatus = "critical";
    } else if (completionRate < 70) {
      healthStatus = "poor";
    } else if (completionRate < 85) {
      healthStatus = "fair";
    } else if (completionRate < 95) {
      healthStatus = "good";
    }

    return {
      totalMilestones,
      completedMilestones,
      inProgressMilestones,
      overdueMilestones,
      completionRate: Math.round(completionRate),
      healthStatus,
      upcomingDeadlines: milestones
        .filter(m => m.endDate && m.endDate > Date.now() && m.status !== "COMPLETED")
        .sort((a, b) => (a.endDate || 0) - (b.endDate || 0))
        .slice(0, 5),
    };
  },
});
