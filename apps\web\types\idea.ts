import { Id } from "@workspace/backend/_generated/dataModel";

export type IdeaStatus = 
  | "INVALIDATED"
  | "VALIDATED" 
  | "FAILED"
  | "IN_PROGRESS"
  | "LAUNCHED";

export interface Idea {
  id: Id<"Idea">;
  name: string;
  description: string;
  industry: string;
  status: IdeaStatus;
  problemSolved?: string;
  solutionOffered?: string;
  aiOverallValidation?: number;
  internal: boolean;
  openSource: boolean;
  ownerId?: Id<"User">;
  organizationId: Id<"Organization">;
  _creationTime: number;
} 