import { useQuery, useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { toast } from "sonner";

export function useFeatureLinks(featureId: string) {
  // Convert featureId string to Convex ID
  const featureConvexId = featureId as Id<"Feature">;

  // Fetch links using Convex
  const links = useQuery(api.projects.feature.getFeatureLinks, {
    featureId: featureConvexId,
  });

  // Mutations for creating and deleting links
  const createLinkMutation = useMutation(api.projects.feature.addFeatureLink);
  const deleteLinkMutation = useMutation(api.projects.feature.deleteFeatureLink);

  const createLink = async ({
    url,
    featureId,
  }: {
    url: string;
    featureId: string;
  }) => {
    try {
      // Extract basic metadata from URL
      const parsedUrl = new URL(url);
      const domain = parsedUrl.hostname.replace("www.", "");

      const result = await createLinkMutation({
        featureId: featureId as Id<"Feature">,
        url,
        title: domain, // Use domain as default title
        siteName: domain,
        favicon: `https://www.google.com/s2/favicons?domain=${url}&sz=64`,
      });
      return { success: true, data: result };
    } catch (error) {
      console.error("Error creating link:", error);
      toast.error("Failed to add link");
      return { success: false, error };
    }
  };

  const deleteLink = async ({ id }: { id: string }) => {
    try {
      await deleteLinkMutation({ id: id as Id<"FeatureLink"> });
      return { success: true };
    } catch (error) {
      console.error("Error deleting link:", error);
      toast.error("Failed to remove link");
      return { success: false, error };
    }
  };

  return {
    links: links || [],
    isLoading: links === undefined,
    createLink,
    isCreating: false, // Convex mutations don't expose loading state the same way
    deleteLink,
    isDeleting: false, // Convex mutations don't expose loading state the same way
  };
}
