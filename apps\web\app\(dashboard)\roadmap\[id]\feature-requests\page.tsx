import { Suspense } from "react";
import { FeatureRequestsClient } from "./_components/feature-requests-client";
import LoadingSpinner from "@workspace/ui/components/loading-spinner";

interface FeatureRequestsPageProps {
  params: Promise<{ id: string }>;
}

export default async function FeatureRequestsPage({
  params,
}: FeatureRequestsPageProps) {
  const { id } = await params;

  return (
      <Suspense fallback={<LoadingSpinner />}>
        <FeatureRequestsClient roadmapId={id} />
      </Suspense>
  );
}
