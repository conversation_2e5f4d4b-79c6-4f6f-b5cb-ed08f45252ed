import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { betterAuth } from "../auth";
import { ConvexError } from "convex/values";

// QUERIES

// Get current organization for the authenticated user
export const getCurrentOrganization = query({
  args: {},
  handler: async (ctx) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    const organization = await ctx.db.get(auth.organizationId);
    if (!organization) throw new ConvexError("Organization not found");

    // Get organization members
    const members = await ctx.db
      .query("User")
      .filter((q) => q.eq(q.field("organizationId"), auth.organizationId))
      .collect();

    // Get organization invitations
    const invitations = await ctx.db
      .query("OrganizationInvitation")
      .filter((q) => q.eq(q.field("organizationId"), auth.organizationId))
      .collect();

    return {
      ...organization,
      members,
      invitations,
    };
  },
});

// Get organization members
export const getOrganizationMembers = query({
  args: {},
  handler: async (ctx) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    const members = await ctx.db
      .query("User")
      .filter((q) => q.eq(q.field("organizationId"), auth.organizationId))
      .collect();

    return members;
  },
});

// Get organization invitations
export const getOrganizationInvitations = query({
  args: {},
  handler: async (ctx) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    const invitations = await ctx.db
      .query("OrganizationInvitation")
      .filter((q) => q.eq(q.field("organizationId"), auth.organizationId))
      .collect();

    return invitations;
  },
});

// MUTATIONS

// Update organization settings
export const updateOrganization = mutation({
  args: {
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    website: v.optional(v.string()),
    logo: v.optional(v.string()),
  },
  handler: async (ctx, { name, description, website, logo }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    const organization = await ctx.db.get(auth.organizationId);
    if (!organization) throw new ConvexError("Organization not found");

    const updates: any = {};
    if (name !== undefined) updates.name = name;
    if (description !== undefined) updates.description = description;
    if (website !== undefined) updates.website = website;
    if (logo !== undefined) updates.logo = logo;

    await ctx.db.patch(auth.organizationId, updates);
    return true;
  },
});

// Invite user to organization
export const inviteUserToOrganization = mutation({
  args: {
    email: v.string(),
    role: v.string(),
  },
  handler: async (ctx, { email, role }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    // Check if user is already a member
    const existingUser = await ctx.db
      .query("User")
      .filter((q) => 
        q.and(
          q.eq(q.field("email"), email),
          q.eq(q.field("organizationId"), auth.organizationId)
        )
      )
      .first();

    if (existingUser) {
      throw new ConvexError("User is already a member of this organization");
    }

    // Check if invitation already exists
    const existingInvitation = await ctx.db
      .query("OrganizationInvitation")
      .filter((q) => 
        q.and(
          q.eq(q.field("email"), email),
          q.eq(q.field("organizationId"), auth.organizationId)
        )
      )
      .first();

    if (existingInvitation) {
      throw new ConvexError("Invitation already sent to this email");
    }

    // Create invitation
    const invitationId = await ctx.db.insert("OrganizationInvitation", {
      organizationId: auth.organizationId,
      email,
      role,
      invitedById: auth.id,
      status: "pending",
      expiresAt: Date.now() + (7 * 24 * 60 * 60 * 1000), // 7 days
    });

    return invitationId;
  },
});

// Revoke organization invitation
export const revokeInvitation = mutation({
  args: { invitationId: v.id("OrganizationInvitation") },
  handler: async (ctx, { invitationId }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    const invitation = await ctx.db.get(invitationId);
    if (!invitation || invitation.organizationId !== auth.organizationId) {
      throw new ConvexError("Invitation not found or access denied");
    }

    await ctx.db.delete(invitationId);
    return true;
  },
});

// Remove user from organization
export const removeUserFromOrganization = mutation({
  args: { userId: v.id("User") },
  handler: async (ctx, { userId }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new ConvexError("Authentication required");

    const user = await ctx.db.get(userId);
    if (!user || user.organizationId !== auth.organizationId) {
      throw new ConvexError("User not found or access denied");
    }

    // Cannot remove yourself
    if (userId === auth.id) {
      throw new ConvexError("Cannot remove yourself from the organization");
    }

    // Update user to remove from organization
    await ctx.db.patch(userId, {
      organizationId: undefined,
      role: undefined,
    });

    return true;
  },
});
