"use client";
import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import IdeaForm from "@/components/idea/core/idea-form";

const NewIdeaForm = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  // Create idea mutation using Convex
  const createIdeaMutation = useMutation(api.ideas.createIdea);

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      const result = await createIdeaMutation({
        ...values,
        status: "INVALIDATED",
      });

      toast.success("Idea created successfully");
      router.push(`/ideas/${result.id}`);
    } catch (error) {
      console.error("Error creating idea:", error);
      toast.error("Failed to create idea. Please try again.");
      throw error; // Re-throw to let the form component handle the loading state
    } finally {
      setLoading(false);
    }
  };

  return (
    <IdeaForm
      mode="create"
      onSubmit={handleSubmit}
      loading={loading}
      variant="full"
    />
  );
};

export default NewIdeaForm;
