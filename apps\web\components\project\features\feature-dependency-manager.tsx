"use client";

import React, { useState, useMemo } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { useSession } from "@/context/session-context";
import { toast } from "sonner";
import { Badge } from "@workspace/ui/components/badge";
import { Button } from "@workspace/ui/components/button";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";
import { X, ArrowRight, ArrowLeft, AlertCircle } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
} from "@workspace/ui/components/dialog";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@workspace/ui/components/alert";
import LoadingSpinner from "@workspace/ui/components/loading-spinner";
import DependencyGraphVisualization from "./dependency-graph-visualization";
import { PhaseSelector } from "@/components/ui/selectors/phase-selector";

interface FeatureDependencyManagerProps {
  featureId: string;
  projectId?: string;
}

interface CombinedDependency {
  id: string;
  name: string;
  description?: string;
  phase?: string;
  priority?: string;
  user?: {
    name?: string;
    image?: string;
    email?: string;
  };
  type: "dependency" | "dependent";
}

const UserAvatar = ({
  user,
}: {
  user?: { name?: string; image?: string; email?: string };
}) => {
  if (!user) return null;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Avatar className="h-5 w-5">
            <AvatarImage src={user.image} alt={user.name} />
            <AvatarFallback className="text-xs bg-muted">
              {user.name?.charAt(0) || "U"}
            </AvatarFallback>
          </Avatar>
        </TooltipTrigger>
        <TooltipContent side="top" className="text-xs">
          <div>
            <p className="font-medium">{user.name || "Unassigned"}</p>
            {user.email && (
              <p className="text-muted-foreground">{user.email}</p>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export const FeatureDependencyManager: React.FC<
  FeatureDependencyManagerProps
> = ({ featureId, projectId }) => {
  const [isAddingDependency, setIsAddingDependency] = useState(false);
  const [selectedFeature, setSelectedFeature] = useState<string | null>(null);
  const [showGraphDialog, setShowGraphDialog] = useState(false);

  // Convex mutations
  const addDependencyMutation = useMutation(api.projects.feature.addFeatureDependency);
  const removeDependencyMutation = useMutation(api.projects.feature.removeFeatureDependency);

  // Convex queries
  const featureDependencies = useQuery(api.projects.feature.getFeatureDependencies, {
    featureId: featureId as Id<"Feature">,
  });

  const availableFeatures = useQuery(api.projects.feature.getAvailableFeatures, {
    projectId: projectId ? (projectId as Id<"Project">) : undefined,
    excludeFeatureId: featureId as Id<"Feature">,
  });

  // Combine dependencies and dependents into a single list
  const combinedDependencies = useMemo(() => {
    if (!featureDependencies) return [];

    const combined: CombinedDependency[] = [];

    // Add dependencies (features this feature depends on)
    featureDependencies.dependencies?.forEach((dep: any) => {
      if (dep.dependency?._id) {
        combined.push({
          id: dep.dependency._id,
          name: dep.dependency.name,
          description: dep.dependency.description,
          phase: dep.dependency.phase,
          priority: dep.dependency.priority,
          user: dep.dependency.assignedTo
            ? {
                name: dep.dependency.assignedTo.name,
                image: dep.dependency.assignedTo.image,
                email: dep.dependency.assignedTo.email,
              }
            : undefined,
          type: "dependency",
        });
      }
    });

    // Add dependents (features that depend on this feature)
    featureDependencies.dependents?.forEach((dep: any) => {
      if (dep.feature?._id) {
        combined.push({
          id: dep.feature._id,
          name: dep.feature.name,
          description: dep.feature.description,
          phase: dep.feature.phase,
          priority: dep.feature.priority,
          user: dep.feature.assignedTo
            ? {
                name: dep.feature.assignedTo.name,
                image: dep.feature.assignedTo.image,
                email: dep.feature.assignedTo.email,
              }
            : undefined,
          type: "dependent",
        });
      }
    });

    return combined.sort((a, b) => a.name.localeCompare(b.name));
  }, [featureDependencies]);

  const handleAddDependency = async () => {
    if (!selectedFeature) return;
    try {
      await addDependencyMutation({
        parentId: selectedFeature as Id<"Feature">,
        dependentFeatureId: featureId as Id<"Feature">,
      });
      toast.success("Dependency added");
      setSelectedFeature(null);
      setIsAddingDependency(false);
    } catch (error) {
      toast.error("Failed to add dependency");
    }
  };

  const handleRemoveDependency = async (parentId: string) => {
    try {
      await removeDependencyMutation({
        parentId: parentId as Id<"Feature">,
        dependentFeatureId: featureId as Id<"Feature">,
      });
      toast.success("Dependency removed");
    } catch (error) {
      toast.error("Failed to remove dependency");
    }
  };

  if (!dependencies?.success) {
    return (
      <div className="flex items-center justify-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Status Alert */}
      {validationResult?.success &&
        validationResult.data &&
        !validationResult.data.canComplete && (
          <Alert>
            <AlertCircle className="h-4 w-4 text-destructive" color="red" />
            <AlertTitle className="text-destructive">
              Blocked by {validationResult.data.blockers.length} incomplete
              dependencies
            </AlertTitle>
            <AlertDescription className="text-muted-foreground">
              {validationResult.data.blockers.map((blocker: any) => (
                <div key={blocker.id}>{blocker.name}</div>
              ))}
            </AlertDescription>
          </Alert>
        )}

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="font-medium">Dependencies</h3>
          <p className="text-sm text-muted-foreground">
            {combinedDependencies.length} related features
          </p>
        </div>

        <div className="flex gap-2">
          <Dialog open={showGraphDialog} onOpenChange={setShowGraphDialog}>
            <DialogTrigger asChild>
              <Button size="sm" variant="dark">
                <ArrowRight className="h-4 w-4 mr-1" />
                Manage
              </Button>
            </DialogTrigger>
            <DialogContent className="w-full space-y-0 h-screen min-w-[100vw] !p-0 flex flex-col">
              <DialogHeader className="border-b py-1.5 space-y-0 m-0">
                <DialogTitle className="text-lg flex-1">
                  Dependency Graph
                </DialogTitle>
                <DialogDescription>
                  Manage the flow of your project's features.
                </DialogDescription>
              </DialogHeader>
              <div className="flex-1 grow p-0 min-h-0 min-w-0 overflow-auto bg-background">
                <DependencyGraphVisualization
                  features={
                    dependencyGraph?.success && dependencyGraph.data
                      ? dependencyGraph.data.features.map((feature: any) => ({
                          id: feature.id,
                          name: feature.name,
                          phase: feature.phase,
                          priority: feature.priority,
                          assignedTo: feature.assignedTo,
                          user: feature.assignedTo
                            ? {
                                name: feature.assignedTo.name,
                                image: feature.assignedTo.image,
                              }
                            : undefined,
                        }))
                      : []
                  }
                  dependencies={
                    dependencyGraph?.success && dependencyGraph.data
                      ? dependencyGraph.data.dependencies
                      : []
                  }
                  currentFeatureId={featureId}
                />
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Dependencies List */}
      {combinedDependencies.length === 0 ? (
        <div className="py-8 text-center text-sm text-muted-foreground">
          No dependencies
        </div>
      ) : (
        <div className="space-y-px divide-y">
          {combinedDependencies.map((item) => (
            <div
              key={`${item.type}-${item.id}`}
              className="group flex items-center justify-between py-2 px-3 -mx-3 hover:bg-muted/50"
            >
              <div className="flex items-center gap-3 min-w-0 flex-1">
                <div className="min-w-0 space-y-2 flex-1">
                  <div className="flex items-center gap-2 text-muted-foreground">
                    {item.phase && (
                      <PhaseSelector disabled={true} phase={item.phase} />
                    )}
                    {item.type === "dependency" ? (
                      <ArrowRight className="h-3 w-3" />
                    ) : (
                      <ArrowLeft className="h-3 w-3" />
                    )}
                    <Badge className="">
                      {item.type === "dependency"
                        ? "blocks this"
                        : "blocked by this"}
                    </Badge>
                  </div>
                  <div>
                    <p className="font-medium text-sm truncate whitespace-pre-wrap line-clamp-1">
                      {item.name}
                    </p>
                    {item.description && (
                      <p className="text-xs text-muted-foreground truncate whitespace-pre-wrap line-clamp-1 mt-0.5">
                        {item.description}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <UserAvatar user={item.user} />

                {item.type === "dependency" && (
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleRemoveDependency(item.id)}
                    className="h-6 w-6 p-0"
                    disabled={removeDependencyMutation.isPending}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default FeatureDependencyManager;
