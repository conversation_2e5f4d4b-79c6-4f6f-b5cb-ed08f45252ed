"use client";

import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { ProjectInfo } from "./project-info";
import { ProjectTabs } from "./tabs";
import { ReactNode } from "react";

interface ProjectContentProps {
  projectId: string;
  token: string;
  children: ReactNode;
}

export function ProjectContent({
  projectId,
  token,
  children,
}: ProjectContentProps) {
  const project = useQuery(api.projects.index.getProjectById, {
    id: projectId as Id<"Project">,
  });

  if (project === undefined) {
    return <div className="p-4">Loading...</div>;
  }
  return (
    <>
      <div className="container space-y-4 p-4">
        <ProjectInfo
          title={project.name}
          description={project?.description || ""}
          platform={project?.platform || ""}
          id={project.id}
          token={token}
        />
      </div>
      <ProjectTabs projectId={project.id} />
      <div className="p-4">{children}</div>
    </>
  );
}
