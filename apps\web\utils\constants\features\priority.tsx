import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>D<PERSON>, <PERSON>Up } from "lucide-react";

export const priorities = [
  {
    id: "HIGH",
    name: "High",
    icon: ArrowUp,
    colorClass: "text-red-600",
  },
  {
    id: "MEDIUM",
    name: "Medium",
    icon: <PERSON><PERSON><PERSON><PERSON><PERSON>,
    colorClass: "text-yellow-600",
  },
  {
    id: "LOW",
    name: "Low",
    icon: ArrowDown,
    colorClass: "text-green-600",
  },
] as const;
