"use client";
import React, { ReactNode } from "react";
import { Separator } from "@workspace/ui/components/separator";
import NoData from "@/components/shared/no-data";
import IdeaInfo from "@/components/idea/core/idea-info";
import { IdeaTabs } from "@/components/idea/core/tabs";
import Header from "@/components/shared/header";
import { ExpandedLayoutContainer } from "@/components/expanded-layout-container";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import LoadingSpinner from "@workspace/ui/components/loading-spinner";

const IdeaLayout = ({
  params,
  children,
}: {
  params: { id: string };
  children: ReactNode;
}) => {
  const { id } = params;

  // Fetch idea using Convex
  const idea = useQuery(api.ideas.getIdea, { id: id as Id<"Idea"> });
  const isPending = idea === undefined;
  const isError = false; // Convex handles errors differently

  const tabsMenu = [
    {
      title: "Overview",
      href: `/ideas/${id}`,
    },
    {
      title: "Business",
      href: `/ideas/${id}/business`,
    },
    {
      title: "Audience",
      href: `/ideas/${id}/audience`,
    },
    {
      title: "Competitors",
      href: `/ideas/${id}/competitors`,
    },
    {
      title: "Pricing",
      href: `/ideas/${id}/pricing`,
    },
  ];

  if (isPending) {
    return <LoadingSpinner />;
  }

  if (isError || !idea) {
    return <NoData />;
  }

  return (
    <div>
      <Header
        crumb={[
          {
            title: "Ideas",
            url: "/ideas",
          },
          {
            title: idea.name,
            url: `/ideas/${id}`,
          },
        ]}
      >
        {null}
      </Header>
      <ExpandedLayoutContainer sidebar={<></>}>
        <div className="container">
          <IdeaInfo id={id} />
          <IdeaTabs tabs={tabsMenu} ideaId={id} />
          {children}
        </div>
      </ExpandedLayoutContainer>
    </div>
  );
};

export default IdeaLayout;
