"use client";
import React, { useState } from "react";
import { IssueStatusField } from "@/components/ui/issue-fields/issue-status-field";
import { IssueLabelField } from "@/components/ui/issue-fields/issue-label-field";
import { IssueDueDateField } from "@/components/ui/issue-fields/issue-due-date-field";
import { IssuePriorityField } from "@/components/ui/issue-fields/issue-priority-field";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { useSession } from "@/context/session-context";
import { useQuery, useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import NoData from "@/components/shared/no-data";
import IssueLinks from "./issue-links";
import { AssigneeSelector } from "@/components/ui/selectors/assignee-selector";
import { MilestoneSelector } from "@/components/ui/selectors/milestone-selector";
import { IssueSelector } from "@/components/ui/selectors/issue-selector";
import { toast } from "sonner";
import { Button } from "@workspace/ui/components/button";
import { Separator } from "@workspace/ui/components/separator";
import { Alert, AlertDescription } from "@workspace/ui/components/alert";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@workspace/ui/components/dialog";
import {
  AlertCircle,
  ArrowRight,
  GitBranch,
  Plus,
  Trash2,
  CheckCircle,
  Target,
  Users,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";
import { StatusSelector } from "@/components/ui/selectors/status-selector";

function IssueSidebarSkeleton() {
  return (
    <div className="container">
      <div className="flex flex-col gap-4">
        <Skeleton className="h-6 w-1/2 mb-2" />
        <div className="grid grid-cols-[140px_1fr] gap-y-3">
          {[...Array(5)].map((_, idx) => (
            <React.Fragment key={idx}>
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-8 w-full" />
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  );
}

const IssueSidebar = ({ issueId }: { issueId: Id<"Issue"> }) => {
  const { token } = useSession();
  const [isAddingDependency, setIsAddingDependency] = useState(false);
  const [selectedIssue, setSelectedIssue] = useState<string | null>(null);

  // Fetch issue details using Convex
  const issue = useQuery(api.issues.index.getIssue, { id: issueId });

  // Fetch dependencies using Convex
  const dependencies = useQuery(api.issues.index.getIssueDependencies, { issueId });

  // Fetch validation result using Convex
  const validationResult = useQuery(api.issues.index.validateIssueCompletion, { issueId });

  // Fetch descendant issues using Convex
  const descendantIssueIds = useQuery(api.issues.index.getAllDescendantIssues, { issueId });

  // Mutations using Convex
  const updateIssueMutation = useMutation(api.issues.index.updateIssue);
  const addDependencyMutation = useMutation(api.issues.index.addIssueDependency);
  const removeDependencyMutation = useMutation(api.issues.index.removeIssueDependency);







  if (issue === undefined) return <IssueSidebarSkeleton />;
  if (issue === null) return <NoData />;

  // Exclude current issue and all its descendants from dependency/parent selection
  const excludedIssueIds = [issueId, ...(descendantIssueIds || [])];

  const handleAddDependency = async () => {
    if (!selectedIssue) return;

    try {
      await addDependencyMutation({
        parentId: selectedIssue as Id<"Issue">,
        dependentIssueId: issueId,
      });
      toast.success("Dependency added successfully");
      setSelectedIssue(null);
      setIsAddingDependency(false);
    } catch (error: any) {
      if (error.message?.includes("circular dependency")) {
        toast.error("Cannot add dependency: would create circular dependency");
      } else if (error.message?.includes("already exists")) {
        toast.error("Dependency already exists");
      } else {
        toast.error("Failed to add dependency");
      }
    }
  };

  const handleRemoveDependency = async (parentId: string) => {
    try {
      await removeDependencyMutation({
        parentId: parentId as Id<"Issue">,
        dependentIssueId: issueId,
      });
      toast.success("Dependency removed successfully");
    } catch (error) {
      toast.error("Failed to remove dependency");
    }
  };

  const DependencyCard = ({
    dep,
    showRemove = false,
    onRemove,
  }: {
    dep: any;
    showRemove?: boolean;
    onRemove?: () => void;
  }) => (
    <div className="flex items-center justify-between p-3 border rounded-lg bg-card hover:bg-muted/30 transition-colors">
      <div className="gap-3 min-w-0 flex-1">
        <div className="flex items-center justify-between gap-2">
          <StatusSelector status={dep.status} disabled />
          {showRemove && onRemove && (
            <Button
              size="sm"
              variant="ghost"
              onClick={onRemove}
              className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
        <div className="min-w-0 mt-1 flex-1">
          <p className="text-sm font-medium line-clamp-2">{dep.title}</p>
        </div>
      </div>
    </div>
  );

  const EmptyState = ({
    icon: Icon,
    title,
    description,
  }: {
    icon: React.ElementType;
    title: string;
    description: string;
  }) => (
    <div className="text-center py-8 text-muted-foreground">
      <Icon className="h-12 w-12 mx-auto mb-3 opacity-40" />
      <p className="text-sm font-medium">{title}</p>
      <p className="text-xs mt-1">{description}</p>
    </div>
  );

  return (
    <div className="p-4 space-y-6">
      <div className="space-y-3">
        <h2 className="text-lg font-semibold"> Attributes</h2>

        <div className="grid grid-cols-[120px_1fr] gap-y-3">
          <h3 className="text-sm font-medium text-muted-foreground">
            Assignee
          </h3>
          <AssigneeSelector
            assignee={
              typeof issue.assignedTo === "string"
                ? issue.assignedTo
                : issue.assignedTo?.id || ""
            }
            onChange={async (e) => {
              try {
                await updateIssueMutation({
                  id: issueId,
                  assignedToId: e as Id<"User">,
                });
                toast.success("Issue assignee updated");
              } catch (error) {
                toast.error("Failed to change issue assignee");
              }
            }}
          />

          <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div>
                  <IssueStatusField
                    issueId={issueId}
                    value={issue?.status}
                    onChange={async (status) => {
                      try {
                        await updateIssueMutation({
                          id: issueId,
                          status: status as any,
                        });
                        toast.success("Issue status updated");
                      } catch (error) {
                        toast.error("Failed to update issue status");
                      }
                    }}
                  />
                </div>
              </TooltipTrigger>
              {validationResult && !validationResult.canComplete && (
                <TooltipContent>
                  <p className="text-xs">
                    Cannot mark as done: blocked by{" "}
                    {validationResult.blockers.length} uncompleted dependencies
                  </p>
                </TooltipContent>
              )}
            </Tooltip>
          </TooltipProvider>

          <h3 className="text-sm font-medium text-muted-foreground">
            Priority
          </h3>
          <IssuePriorityField
            issueId={issueId}
            value={issue?.priority}
            onChange={async (priority) => {
              try {
                await updateIssueMutation({
                  id: issueId,
                  priority: priority as any,
                });
                toast.success("Issue priority updated");
              } catch (error) {
                toast.error("Failed to update issue priority");
              }
            }}
          />

          <h3 className="text-sm font-medium text-muted-foreground">Label</h3>
          <IssueLabelField
            issueId={issueId}
            value={issue?.label}
            onChange={async (label) => {
              updateIssueMutation.mutate({ label });
            }}
          />

          <h3 className="text-sm font-medium text-muted-foreground">
            Due Date
          </h3>
          <IssueDueDateField
            issueId={issueId}
            value={issue?.dueDate ? new Date(issue?.dueDate) : null}
            onChange={async (dueDate) => {
              updateIssueMutation.mutate({ dueDate: dueDate || undefined });
            }}
          />

          <h3 className="text-sm font-medium text-muted-foreground">
            Milestone
          </h3>
          <MilestoneSelector
            projectId={issue.projectId}
            value={issue.milestoneId || undefined}
            onValueChange={async (milestoneId) => {
              updateIssueMutation.mutate({
                milestoneId,
              });
            }}
          />
        </div>
      </div>

      <Separator />

      {/* Dependencies Section with Tabs */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="font-medium text-muted-foreground">Dependencies</h3>
          <Dialog
            open={isAddingDependency}
            onOpenChange={setIsAddingDependency}
          >
            <DialogTrigger asChild>
              <Button size="sm" variant="outline" className="h-8">
                <Plus className="h-3 w-3 mr-1" />
                Add
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add Issue Dependency</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Select an issue that this issue depends on. This issue will be
                  blocked until the selected issue is completed.
                </p>
                <IssueSelector
                  projectId={issue.projectId}
                  value={selectedIssue || ""}
                  onValueChange={(value) => setSelectedIssue(value)}
                  excludeIssueId={excludedIssueIds}
                  placeholder="Select dependency issue..."
                />
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setIsAddingDependency(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleAddDependency}
                    disabled={!selectedIssue || addDependencyMutation.isPending}
                  >
                    {addDependencyMutation.isPending
                      ? "Adding..."
                      : "Add Dependency"}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Completion Status */}
        {validationResult && (
          <Alert
            className={`${
              validationResult.canComplete
                ? "border-green-200 bg-green-50"
                : "border-red-200 bg-red-50"
            }`}
          >
            <AlertDescription className="text-xs">
              {validationResult.canComplete ? (
                <span className="text-green-800 flex items-center gap-1">
                  <CheckCircle className="h-3 w-3" />
                  Ready to complete
                </span>
              ) : (
                <span className="text-red-800 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  Blocked by {validationResult.blockers.length} dependencies
                </span>
              )}
            </AlertDescription>
          </Alert>
        )}

        {/* Warning for DONE issues that are blocked */}
        {validationResult &&
          !validationResult.canComplete &&
          issue?.status === "DONE" && (
            <Alert className="border-orange-200 bg-orange-50">
              <AlertDescription className="text-xs">
                <span className="text-orange-800 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  Warning: Issue is marked as DONE but has uncompleted
                  dependencies
                </span>
              </AlertDescription>
            </Alert>
          )}

        {/* Dependencies Tabs */}
        <Tabs defaultValue="dependencies" className="w-full">
          <TabsList className="grid w-full grid-cols-2 h-9">
            <TabsTrigger value="dependencies" className="text-xs">
              <ArrowRight className="h-3 w-3 mr-1" />
              Depends on ({dependencies?.dependencies.length || 0})
            </TabsTrigger>
            <TabsTrigger value="dependents" className="text-xs">
              <GitBranch className="h-3 w-3 mr-1" />
              Blocks ({dependencies?.dependents.length || 0})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="dependencies" className="mt-4 space-y-3">
            {dependencies && dependencies.dependencies.length > 0 ? (
              <div className="space-y-2">
                {dependencies.dependencies.map((dep, index) => (
                  <DependencyCard
                    key={`${dep.id}-${index}`}
                    dep={dep}
                    showRemove={true}
                    onRemove={() => dep.id && handleRemoveDependency(dep.id)}
                  />
                ))}
              </div>
            ) : (
              <EmptyState
                icon={Target}
                title="No dependencies"
                description="This issue can be worked on independently"
              />
            )}
          </TabsContent>

          <TabsContent value="dependents" className="mt-4 space-y-3">
            {dependencies && dependencies.dependents.length > 0 ? (
              <div className="space-y-2">
                {dependencies.dependents.map((dep, index) => (
                  <DependencyCard key={`${dep.id}-${index}`} dep={dep} />
                ))}
              </div>
            ) : (
              <EmptyState
                icon={Users}
                title="No dependents"
                description="No other issues depend on this one"
              />
            )}
          </TabsContent>
        </Tabs>
      </div>

      <Separator />

      <IssueLinks issueId={issueId as any} />
    </div>
  );
};

export default IssueSidebar;
