import { v } from "convex/values";
import { defineTable } from "convex/server";
import { IdeaStatus, Importance, SwotType } from "./enum";

// Idea schema
export const Idea = defineTable({
  name: v.string(),
  description: v.string(),
  industry: v.string(),
  ownerId: v.optional(v.id("users")),
  organizationId: v.id("organizations"),
  internal: v.boolean(),
  openSource: v.boolean(),
  status: IdeaStatus,
  aiOverallValidation: v.optional(v.number()),
  problemSolved: v.optional(v.string()),
  solutionOffered: v.optional(v.string()),
})
  .index("by_organization", ["organizationId"])
  .index("by_owner", ["ownerId"])
  .index("by_status", ["status"])
  .index("by_industry", ["industry"])
  .index("by_organization_status", ["organizationId", "status"])
  .index("by_organization_owner", ["organizationId", "ownerId"])
  .index("by_internal", ["internal"])
  .index("by_open_source", ["openSource"]);

// Competitor schema
export const Competitor = defineTable({
  ideaId: v.id("ideas"),

  // Basic Info (AI-generated)
  name: v.string(),
  website: v.optional(v.string()),
  description: v.optional(v.string()),
  logoUrl: v.optional(v.string()),

  // Market Position (AI-generated)
  marketShare: v.optional(v.number()), // Percentage
  annualRevenue: v.optional(v.number()), // In millions USD
  employeeCount: v.optional(v.string()),
  foundedYear: v.optional(v.number()),
  headquarters: v.optional(v.string()),

  targetAudience: v.optional(v.string()),
  threatLevel: Importance,

  // Performance Metrics (AI-generated)
  userGrowthRate: v.optional(v.number()),
  churnRate: v.optional(v.number()),
  customerSatisfaction: v.optional(v.number()),
  marketCap: v.optional(v.number()), // If public company

  // Tracking
  lastUpdated: v.number(),
  isActive: v.optional(v.boolean()),
})
  .index("by_idea", ["ideaId"])
  .index("by_threat_level", ["threatLevel"])
  .index("by_active", ["isActive"])
  .index("by_idea_threat", ["ideaId", "threatLevel"])
  .index("by_idea_active", ["ideaId", "isActive"])
  .index("by_last_updated", ["lastUpdated"])
  .index("by_market_share", ["marketShare"])
  .index("by_annual_revenue", ["annualRevenue"]);

// CompetitiveMove schema
export const CompetitiveMove = defineTable({
  competitorId: v.optional(v.id("competitors")),

  moveType: v.string(),
  title: v.string(),
  description: v.string(),

  // Impact Analysis (AI-generated)
  impactLevel: Importance,
  targetAudience: v.optional(v.string()),
  affectedFeatures: v.array(v.string()),

  // Timing
  announcedDate: v.optional(v.number()),
  launchDate: v.optional(v.number()),
  completionDate: v.optional(v.number()),

  userFeedback: v.optional(v.string()),
  pressCoverage: v.array(v.string()),

  // Strategic Implications (AI-generated)
  opportunities: v.array(v.string()),
  threats: v.array(v.string()),
  responseRequired: v.optional(v.boolean()),
  responseStrategy: v.optional(v.string()),
})
  .index("by_competitor", ["competitorId"])
  .index("by_move_type", ["moveType"])
  .index("by_impact_level", ["impactLevel"])
  .index("by_announced_date", ["announcedDate"])
  .index("by_launch_date", ["launchDate"])
  .index("by_completion_date", ["completionDate"])
  .index("by_response_required", ["responseRequired"])
  .index("by_competitor_impact", ["competitorId", "impactLevel"])
  .index("by_competitor_type", ["competitorId", "moveType"]);

// CompetitorSwot schema
export const CompetitorSwot = defineTable({
  impact: v.optional(Importance),
  type: SwotType,
  swotAnalysis: v.string(),
  competitorId: v.optional(v.id("competitors")),
})
  .index("by_competitor", ["competitorId"])
  .index("by_type", ["type"])
  .index("by_impact", ["impact"])
  .index("by_competitor_type", ["competitorId", "type"])
  .index("by_competitor_impact", ["competitorId", "impact"]);
