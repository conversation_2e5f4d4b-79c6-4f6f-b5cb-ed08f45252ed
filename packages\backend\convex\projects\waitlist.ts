import { v } from "convex/values";
import { mutation, query, action } from "../_generated/server";
import { counter } from "../SharedCounter";
import { ConvexError } from "convex/values";
import { betterAuth } from "../auth";

// Utility function for generating referral codes
function generateReferralCode(prefix = "", length = 10) {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  const randomChars = Array.from(
    { length },
    () => chars[Math.floor(Math.random() * chars.length)]
  ).join("");
  return `${prefix}${randomChars}`;
}

// Get waitlist by slug (public query - no auth required)
export const getWaitlistBySlug = query({
  args: {
    slug: v.string(),
  },
  handler: async (ctx, { slug }) => {
    // Find waitlist by slug
    const waitlist = await ctx.db
      .query("Waitlist")
      .withIndex("by_slug", (q) => q.eq("slug", slug))
      .first();

    if (!waitlist) {
      return null;
    }

    const [totalEntries, referralCount, project] = await Promise.all([
      counter.count(ctx, `waitlist-entries-${waitlist._id}`),
      counter.count(ctx, `waitlist-referrals-${waitlist._id}`),
      ctx.db.get(waitlist.projectId),
    ]);

    return {
      ...waitlist,
      project,
      stats: {
        totalEntries,
        joinedCount: totalEntries,
        referralCount,
      },
    };
  },
});

// Check waitlist entry status by email (mutation)
export const checkWaitlistEntry = mutation({
  args: {
    waitlistId: v.id("Waitlist"),
    email: v.string(),
  },
  handler: async (ctx, { waitlistId, email }) => {
    // Find the entry
    const entry = await ctx.db
      .query("WaitlistEntry")
      .withIndex("by_waitlist_email", (q) =>
        q.eq("waitlistId", waitlistId).eq("email", email)
      )
      .first();

    if (!entry) {
      return null;
    }

    // Get referral count from counter and referral details
    const [referralCount] = await Promise.all([
      counter.count(ctx, `waitlist-referrals-${waitlistId}`),
    ]);

    return {
      ...entry,
      referralCount,
    };
  },
});

// Join waitlist mutation
export const joinWaitlist = mutation({
  args: {
    waitlistId: v.id("Waitlist"),
    email: v.string(),
    name: v.string(),
    referralCode: v.optional(v.string()),
    utmSource: v.optional(v.string()),
    utmMedium: v.optional(v.string()),
    utmCampaign: v.optional(v.string()),
    ipAddress: v.string(),
  },
  handler: async (ctx, args) => {
    const {
      waitlistId,
      email,
      name,
      referralCode,
      utmSource,
      utmMedium,
      utmCampaign,
      ipAddress,
    } = args;

    const waitlist = await ctx.db.get(waitlistId);

    if (!waitlist) {
      throw new Error("Waitlist does not exist");
    }
    // Check if user already exists
    const existingEntry = await ctx.db
      .query("WaitlistEntry")
      .withIndex("by_waitlist_email", (q) =>
        q.eq("waitlistId", waitlistId).eq("email", email)
      )
      .first();

    if (existingEntry) {
      throw new Error("You're already on the waitlist!");
    }

    // Generate unique referral code with retries
    let attempts = 0;
    let newReferralCode: string;
    let existingCode;

    do {
      if (attempts >= 5) {
        // After 5 attempts, include a timestamp to ensure uniqueness
        newReferralCode = generateReferralCode(
          Date.now().toString(36).slice(-2)
        );
      } else {
        newReferralCode = generateReferralCode();
      }

      existingCode = await ctx.db
        .query("WaitlistEntry")
        .withIndex("by_referral_code", (q) =>
          q.eq("waitlistId", waitlist._id).eq("referralCode", newReferralCode)
        )
        .first();

      attempts++;
    } while (existingCode && attempts < 10);

    if (existingCode) {
      throw new Error("Failed to generate unique referral code");
    }

    // Get current position by incrementing total entries count
    const position =
      (await counter.count(ctx, `waitlist-entries-${waitlistId}`)) + 1;

    // Create waitlist entry
    const entryId = await ctx.db.insert("WaitlistEntry", {
      waitlistId,
      email,
      name,
      referralCode: newReferralCode,
      status: "PENDING",
      position,
      ipAddress: ipAddress || "0.0.0.0",
      utmSource,
      utmMedium,
      utmCampaign,
    });

    await counter.inc(ctx, `waitlist-entries-${waitlistId}`);

    // Handle referral if provided
    let referrerId = null;
    if (referralCode) {
      const referrer = await ctx.db
        .query("WaitlistEntry")
        .withIndex("by_referral_code", (q) =>
          q.eq("waitlistId", waitlist._id).eq("referralCode", referralCode)
        )
        .first();

      if (referrer && referrer.waitlistId === waitlistId) {
        referrerId = referrer._id;

        // Create referral record and increment referral counter
        await Promise.all([
          ctx.db.insert("Referral", {
            waitlistId,
            ipAddress,
            organizationId: waitlist.organizationId,
            referredEmail: email,
            referrerCode: referralCode,
            referrerId: referrer._id,
          }),
          counter.inc(ctx, `waitlist-referrals-${waitlistId}`),
        ]);
      }
    }

    return {
      success: true,
      entry: {
        _id: entryId,
        email,
        name,
        referralCode: newReferralCode,
        position,
        referrerId,
      },
    };
  },
});

// Get live waitlist stats (for real-time updates)
export const getLiveWaitlistStats = query({
  args: {
    waitlistId: v.id("Waitlist"),
  },
  handler: async (ctx, { waitlistId }) => {
    // Get total entries and joined count using counters
    const [totalEntries, referralCount] = await Promise.all([
      counter.count(ctx, `waitlist-entries-${waitlistId}`),
      counter.count(ctx, `waitlist-referrals-${waitlistId}`),
    ]);

    // Get today's entries count
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);

    return {
      totalEntries,
      joinedCount: totalEntries,
      referralCount,
    };
  },
});

export const getPublicWaitlistById = query({
  args: { id: v.id("Waitlist") },
  handler: async (ctx, { id }) => {
    const waitlist = await ctx.db.get(id);
    return waitlist;
  },
});

// Create a new waitlist (authenticated)
export const createWaitlist = mutation({
  args: {
    projectId: v.id("Project"),
    name: v.string(),
    slug: v.string(),
    description: v.string(),
    isPublic: v.boolean(),
    allowNameCapture: v.boolean(),
    showPosition: v.boolean(),
    showSocialProof: v.boolean(),
    customMessage: v.optional(v.string()),
    emailSyncEnabled: v.optional(v.boolean()),
    integrationId: v.optional(v.id("Integration")),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new ConvexError("Authentication required");
    }

    // Check if slug is unique
    const existingWaitlist = await ctx.db
      .query("Waitlist")
      .withIndex("by_slug", (q) => q.eq("slug", args.slug))
      .first();

    if (existingWaitlist) {
      throw new ConvexError("Slug already exists");
    }

    // Extract integration-related fields
    const { emailSyncEnabled, integrationId, ...waitlistData } = args;

    // Create waitlist
    const waitlistId = await ctx.db.insert("Waitlist", {
      ...waitlistData,
      organizationId: identity.organizationId,
      createdById: identity.id,
    });

    // If email sync is enabled and integration ID is provided, create integration usage
    if (emailSyncEnabled && integrationId) {
      await ctx.db.insert("IntegrationUsage", {
        integrationId,
        entityType: "waitlist",
        entityId: waitlistId,
        purpose: "email_sync",
        isActive: true,
      });
    }

    const waitlist = await ctx.db.get(waitlistId);
    return {
      id: waitlist!._id,
      name: waitlist!.name,
      slug: waitlist!.slug,
      description: waitlist!.description,
      isPublic: waitlist!.isPublic,
      allowNameCapture: waitlist!.allowNameCapture,
      showPosition: waitlist!.showPosition,
      showSocialProof: waitlist!.showSocialProof,
      customMessage: waitlist!.customMessage,
      organizationId: waitlist!.organizationId,
      projectId: waitlist!.projectId,
      createdById: waitlist!.createdById,
      createdAt: new Date(waitlist!._creationTime).toISOString(),
    };
  },
});

// Get waitlist by ID (authenticated)
export const getWaitlist = query({
  args: { id: v.id("Waitlist") },
  handler: async (ctx, { id }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new ConvexError("Authentication required");
    }

    const user = await ctx.db.get(userId);
    if (!user?.organizationId) {
      throw new ConvexError("User must belong to an organization");
    }

    const waitlist = await ctx.db.get(id);
    if (!waitlist || waitlist.organizationId !== user.organizationId) {
      throw new ConvexError("Waitlist not found or access denied");
    }

    const project = await ctx.db.get(waitlist.projectId);

    // Get integration usage for email sync
    const emailSyncUsage = await ctx.db
      .query("IntegrationUsage")
      .filter((q) => 
        q.and(
          q.eq(q.field("entityType"), "waitlist"),
          q.eq(q.field("entityId"), id),
          q.eq(q.field("purpose"), "email_sync"),
          q.eq(q.field("isActive"), true)
        )
      )
      .first();

    // Transform to match server action format
    return {
      id: waitlist._id,
      name: waitlist.name,
      slug: waitlist.slug,
      description: waitlist.description,
      isPublic: waitlist.isPublic,
      allowNameCapture: waitlist.allowNameCapture,
      showPosition: waitlist.showPosition,
      showSocialProof: waitlist.showSocialProof,
      customMessage: waitlist.customMessage,
      organizationId: waitlist.organizationId,
      projectId: waitlist.projectId,
      createdById: waitlist.createdById,
      createdAt: new Date(waitlist._creationTime).toISOString(),
      project,
      emailSyncEnabled: !!emailSyncUsage,
      integrationId: emailSyncUsage?.integrationId || null,
    };
  },
});

// Get all waitlists for organization (authenticated)
export const getAllWaitlists = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new ConvexError("Authentication required");
    }

    const user = await ctx.db.get(userId);
    if (!user?.organizationId) {
      throw new ConvexError("User must belong to an organization");
    }

    const waitlists = await ctx.db
      .query("Waitlist")
      .filter((q) => q.eq(q.field("organizationId"), user.organizationId))
      .collect();

    // Get stats for each waitlist
    const waitlistsWithStats = await Promise.all(
      waitlists.map(async (waitlist) => {
        const [totalEntries, referralCount, project] = await Promise.all([
          counter.count(ctx, `waitlist-entries-${waitlist._id}`),
          counter.count(ctx, `waitlist-referrals-${waitlist._id}`),
          ctx.db.get(waitlist.projectId),
        ]);

        // Get verified entries count
        const verifiedEntries = await ctx.db
          .query("WaitlistEntry")
          .withIndex("by_waitlist", (q) => q.eq("waitlistId", waitlist._id))
          .filter((q) => q.neq(q.field("verifiedAt"), undefined))
          .collect();

        // Transform to match server action format
        return {
          id: waitlist._id,
          name: waitlist.name,
          slug: waitlist.slug,
          description: waitlist.description,
          isPublic: waitlist.isPublic,
          allowNameCapture: waitlist.allowNameCapture,
          showPosition: waitlist.showPosition,
          showSocialProof: waitlist.showSocialProof,
          customMessage: waitlist.customMessage,
          organizationId: waitlist.organizationId,
          projectId: waitlist.projectId,
          createdById: waitlist.createdById,
          createdAt: new Date(waitlist._creationTime).toISOString(),
          project,
          stats: {
            totalEntries,
            verifiedEntries: verifiedEntries.length,
            totalReferrals: referralCount,
          },
        };
      })
    );

    return waitlistsWithStats;
  },
});

// Update waitlist (authenticated)
export const updateWaitlist = mutation({
  args: {
    id: v.id("Waitlist"),
    name: v.optional(v.string()),
    slug: v.optional(v.string()),
    description: v.optional(v.string()),
    isPublic: v.optional(v.boolean()),
    allowNameCapture: v.optional(v.boolean()),
    showPosition: v.optional(v.boolean()),
    showSocialProof: v.optional(v.boolean()),
    customMessage: v.optional(v.string()),
    emailSyncEnabled: v.optional(v.boolean()),
    integrationId: v.optional(v.id("Integration")),
  },
  handler: async (ctx, { id, emailSyncEnabled, integrationId, ...updates }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new ConvexError("Authentication required");
    }

    const user = await ctx.db.get(userId);
    if (!user?.organizationId) {
      throw new ConvexError("User must belong to an organization");
    }

    const waitlist = await ctx.db.get(id);
    if (!waitlist || waitlist.organizationId !== user.organizationId) {
      throw new ConvexError("Waitlist not found or access denied");
    }

    // Check slug uniqueness if updating slug
    if (updates.slug && updates.slug !== waitlist.slug) {
      const existingWaitlist = await ctx.db
        .query("Waitlist")
        .withIndex("by_slug", (q) => q.eq("slug", updates.slug))
        .first();

      if (existingWaitlist) {
        throw new ConvexError("Slug already exists");
      }
    }

    // Update waitlist data
    await ctx.db.patch(id, updates);

    // Handle integration usage
    if (emailSyncEnabled !== undefined || integrationId !== undefined) {
      // Check if integration usage already exists
      const existingUsage = await ctx.db
        .query("IntegrationUsage")
        .filter((q) => 
          q.and(
            q.eq(q.field("entityType"), "waitlist"),
            q.eq(q.field("entityId"), id),
            q.eq(q.field("purpose"), "email_sync")
          )
        )
        .first();

      if (emailSyncEnabled && integrationId) {
        if (existingUsage) {
          // Update existing usage
          await ctx.db.patch(existingUsage._id, {
            integrationId,
            isActive: true,
          });
        } else {
          // Create new usage
          await ctx.db.insert("IntegrationUsage", {
            integrationId,
            entityType: "waitlist",
            entityId: id,
            purpose: "email_sync",
            isActive: true,
          });
        }
      } else if (!emailSyncEnabled && existingUsage) {
        // Deactivate existing usage
        await ctx.db.patch(existingUsage._id, { isActive: false });
      }
    }

    const updatedWaitlist = await ctx.db.get(id);
    return {
      id: updatedWaitlist!._id,
      name: updatedWaitlist!.name,
      slug: updatedWaitlist!.slug,
      description: updatedWaitlist!.description,
      isPublic: updatedWaitlist!.isPublic,
      allowNameCapture: updatedWaitlist!.allowNameCapture,
      showPosition: updatedWaitlist!.showPosition,
      showSocialProof: updatedWaitlist!.showSocialProof,
      customMessage: updatedWaitlist!.customMessage,
      organizationId: updatedWaitlist!.organizationId,
      projectId: updatedWaitlist!.projectId,
      createdById: updatedWaitlist!.createdById,
      createdAt: new Date(updatedWaitlist!._creationTime).toISOString(),
    };
  },
});

// Delete waitlist (authenticated)
export const deleteWaitlist = mutation({
  args: { id: v.id("Waitlist") },
  handler: async (ctx, { id }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new ConvexError("Authentication required");
    }

    const user = await ctx.db.get(userId);
    if (!user?.organizationId) {
      throw new ConvexError("User must belong to an organization");
    }

    const waitlist = await ctx.db.get(id);
    if (!waitlist || waitlist.organizationId !== user.organizationId) {
      throw new ConvexError("Waitlist not found or access denied");
    }

    // Delete the waitlist (related entries and referrals will be handled by cascade)
    await ctx.db.delete(id);
    
    return { success: true };
  },
});

// Get comprehensive analytics for a waitlist
export const getWaitlistAnalytics = query({
  args: { waitlistId: v.id("Waitlist") },
  handler: async (ctx, { waitlistId }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new ConvexError("Authentication required");
    }

    const user = await ctx.db.get(userId);
    if (!user?.organizationId) {
      throw new ConvexError("User must belong to an organization");
    }

    // Ensure the waitlist belongs to the org
    const waitlist = await ctx.db.get(waitlistId);
    if (!waitlist || waitlist.organizationId !== user.organizationId) {
      throw new ConvexError("Waitlist not found or access denied");
    }

    // Fetch all entries with their data
    const entries = await ctx.db
      .query("WaitlistEntry")
      .withIndex("by_waitlist", (q) => q.eq("waitlistId", waitlistId))
      .collect();

    // Get referral counts for each entry
    const entriesWithReferrals = await Promise.all(
      entries.map(async (entry) => {
        const referralCount = await ctx.db
          .query("Referral")
          .withIndex("by_referrer", (q) => q.eq("referrerId", entry._id))
          .collect();
        return {
          ...entry,
          referralCount: referralCount.length,
        };
      })
    );

    // Calculate comprehensive analytics
    const totalEntries = entriesWithReferrals.length;
    const totalReferrals = entriesWithReferrals.reduce(
      (sum, entry) => sum + entry.referralCount,
      0
    );

    // Status breakdowns
    const verifiedCount = entriesWithReferrals.filter((e) => e.verifiedAt).length;
    const invitedCount = entriesWithReferrals.filter(
      (e) => e.status === "invited" || e.status === "joined"
    ).length;
    const joinedCount = entriesWithReferrals.filter((e) => e.status === "joined").length;
    const activeReferrers = entriesWithReferrals.filter((e) => e.referralCount > 0).length;

    // UTM source breakdown
    const utmSources = entriesWithReferrals.reduce(
      (acc, entry) => {
        const source = entry.utmSource || "direct";
        acc[source] = (acc[source] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    // Status breakdown
    const statusBreakdown = entriesWithReferrals.reduce(
      (acc, entry) => {
        acc[entry.status] = (acc[entry.status] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    // Recent activity (last 7 days)
    const sevenDaysAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
    const recentEntries = entriesWithReferrals.filter(
      (e) => e._creationTime > sevenDaysAgo
    ).length;
    const recentVerifications = entriesWithReferrals.filter(
      (e) => e.verifiedAt && e.verifiedAt > sevenDaysAgo
    ).length;

    // Conversion rates
    const verificationRate =
      totalEntries > 0 ? (verifiedCount / totalEntries) * 100 : 0;
    const invitationRate =
      verifiedCount > 0 ? (invitedCount / verifiedCount) * 100 : 0;
    const joinRate = invitedCount > 0 ? (joinedCount / invitedCount) * 100 : 0;
    const overallConversionRate =
      totalEntries > 0 ? (joinedCount / totalEntries) * 100 : 0;
    const avgReferralsPerUser =
      totalEntries > 0 ? totalReferrals / totalEntries : 0;

    return {
      // Basic counts
      totalEntries,
      totalReferrals,
      verifiedCount,
      invitedCount,
      joinedCount,
      activeReferrers,
      recentEntries,
      recentVerifications,

      // Breakdowns
      utmSources,
      statusBreakdown,

      // Rates and percentages
      verificationRate: Math.round(verificationRate),
      invitationRate: Math.round(invitationRate),
      joinRate: Math.round(joinRate),
      overallConversionRate: Math.round(overallConversionRate * 10) / 10, // Keep 1 decimal
      avgReferralsPerUser: Math.round(avgReferralsPerUser * 10) / 10, // Keep 1 decimal

      // Raw entries for detailed analysis (if needed)
      entries: entriesWithReferrals.map((entry) => ({
        ...entry,
        id: entry._id,
        createdAt: new Date(entry._creationTime).toISOString(),
        verifiedAt: entry.verifiedAt ? new Date(entry.verifiedAt).toISOString() : undefined,
        invitedAt: entry.invitedAt ? new Date(entry.invitedAt).toISOString() : undefined,
        joinedAt: entry.joinedAt ? new Date(entry.joinedAt).toISOString() : undefined,
      })),
    };
  },
});

// Get waitlist entries with filtering and pagination
export const getWaitlistEntries = query({
  args: {
    waitlistId: v.id("Waitlist"),
    search: v.optional(v.string()),
    status: v.optional(v.string()),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
  },
  handler: async (ctx, { waitlistId, search, status, limit = 100, offset = 0 }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new ConvexError("Authentication required");
    }

    const user = await ctx.db.get(userId);
    if (!user?.organizationId) {
      throw new ConvexError("User must belong to an organization");
    }

    // Ensure the waitlist belongs to the org
    const waitlist = await ctx.db.get(waitlistId);
    if (!waitlist || waitlist.organizationId !== user.organizationId) {
      throw new ConvexError("Waitlist not found or access denied");
    }

    // Get all entries for the waitlist first
    let entries = await ctx.db
      .query("WaitlistEntry")
      .withIndex("by_waitlist", (q) => q.eq("waitlistId", waitlistId))
      .collect();

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase();
      entries = entries.filter(
        (entry) =>
          entry.email.toLowerCase().includes(searchLower) ||
          (entry.name && entry.name.toLowerCase().includes(searchLower))
      );
    }

    // Apply status filter
    if (status && status !== "all") {
      entries = entries.filter((entry) => entry.status === status);
    }

    // Sort by creation time (newest first)
    entries.sort((a, b) => b._creationTime - a._creationTime);

    // Get total count before pagination
    const totalCount = entries.length;

    // Apply pagination
    const paginatedEntries = entries.slice(offset, offset + limit);

    // Get referral counts for paginated entries
    const entriesWithReferrals = await Promise.all(
      paginatedEntries.map(async (entry) => {
        const referrals = await ctx.db
          .query("Referral")
          .withIndex("by_referrer", (q) => q.eq("referrerId", entry._id))
          .collect();

        return {
          ...entry,
          id: entry._id,
          createdAt: new Date(entry._creationTime).toISOString(),
          verifiedAt: entry.verifiedAt ? new Date(entry.verifiedAt).toISOString() : undefined,
          invitedAt: entry.invitedAt ? new Date(entry.invitedAt).toISOString() : undefined,
          joinedAt: entry.joinedAt ? new Date(entry.joinedAt).toISOString() : undefined,
          referrals,
        };
      })
    );

    return {
      entries: entriesWithReferrals,
      totalCount,
      hasMore: offset + limit < totalCount,
    };
  },
});

// Update entry status
export const updateEntryStatus = mutation({
  args: {
    entryId: v.id("WaitlistEntry"),
    status: v.string(),
  },
  handler: async (ctx, { entryId, status }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new ConvexError("Authentication required");
    }

    const user = await ctx.db.get(userId);
    if (!user?.organizationId) {
      throw new ConvexError("User must belong to an organization");
    }

    // Get the entry and verify access
    const entry = await ctx.db.get(entryId);
    if (!entry) {
      throw new ConvexError("Entry not found");
    }

    const waitlist = await ctx.db.get(entry.waitlistId);
    if (!waitlist || waitlist.organizationId !== user.organizationId) {
      throw new ConvexError("Access denied");
    }

    // Prepare update data with timestamps
    const updateData: any = { status };

    if (status === "verified") {
      updateData.verifiedAt = Date.now();
    } else if (status === "invited") {
      updateData.invitedAt = Date.now();
    } else if (status === "joined") {
      updateData.joinedAt = Date.now();
    }

    await ctx.db.patch(entryId, updateData);
    
    const updatedEntry = await ctx.db.get(entryId);
    return {
      ...updatedEntry,
      id: updatedEntry!._id,
      createdAt: new Date(updatedEntry!._creationTime).toISOString(),
      verifiedAt: updatedEntry!.verifiedAt ? new Date(updatedEntry!.verifiedAt).toISOString() : undefined,
      invitedAt: updatedEntry!.invitedAt ? new Date(updatedEntry!.invitedAt).toISOString() : undefined,
      joinedAt: updatedEntry!.joinedAt ? new Date(updatedEntry!.joinedAt).toISOString() : undefined,
    };
  },
});

// Delete entry
export const deleteEntry = mutation({
  args: { entryId: v.id("WaitlistEntry") },
  handler: async (ctx, { entryId }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new ConvexError("Authentication required");
    }

    const user = await ctx.db.get(userId);
    if (!user?.organizationId) {
      throw new ConvexError("User must belong to an organization");
    }

    // Get the entry and verify access
    const entry = await ctx.db.get(entryId);
    if (!entry) {
      throw new ConvexError("Entry not found");
    }

    const waitlist = await ctx.db.get(entry.waitlistId);
    if (!waitlist || waitlist.organizationId !== user.organizationId) {
      throw new ConvexError("Access denied");
    }

    await ctx.db.delete(entryId);
    return { success: true };
  },
});

// Check slug availability action
export const checkSlugAvailability = action({
  args: {
    slug: v.string(),
    excludeId: v.optional(v.id("Waitlist")),
  },
  handler: async (ctx, { slug, excludeId }) => {
    try {
      // Query for existing waitlist with the slug
      const existingWaitlists = await ctx.runQuery("projects/waitlist:findWaitlistBySlug", { slug });
      
      // If we're editing an existing waitlist, exclude it from the check
      const isAvailable = excludeId 
        ? !existingWaitlists || existingWaitlists._id === excludeId
        : !existingWaitlists;

      return {
        available: isAvailable,
        slug,
      };
    } catch (error) {
      console.error("Error checking slug availability:", error);
      throw new ConvexError("Failed to check slug availability");
    }
  },
});

// Helper query for slug checking
export const findWaitlistBySlug = query({
  args: { slug: v.string() },
  handler: async (ctx, { slug }) => {
    return await ctx.db
      .query("Waitlist")
      .withIndex("by_slug", (q) => q.eq("slug", slug))
      .first();
  },
});
