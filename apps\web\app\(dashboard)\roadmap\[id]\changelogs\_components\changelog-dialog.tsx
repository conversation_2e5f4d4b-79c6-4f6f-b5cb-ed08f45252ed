"use client";

import { useState, useEffect } from "react";
import { useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Textarea } from "@workspace/ui/components/textarea";
import { Label } from "@workspace/ui/components/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@workspace/ui/components/dialog";
import { toast } from "sonner";
import { DateSelector } from "@/components/ui/selectors";

interface ChangelogDialogProps {
  isOpen: boolean;
  onClose: () => void;
  roadmapId: string;
  editingChangelog?: any;
}

export function ChangelogDialog({
  isOpen,
  onClose,
  roadmapId,
  editingChangelog,
}: ChangelogDialogProps) {
  const isEditing = !!editingChangelog;
  const createChangelogMutation = useMutation(api.projects.roadmapChangelog.createRoadmapChangelog);
  const updateChangelogMutation = useMutation(api.projects.roadmapChangelog.updateRoadmapChangelog);

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    version: "",
    publishDate: new Date(),
    isPublished: false,
  });

  // Initialize form with editing data
  useEffect(() => {
    if (editingChangelog) {
      setFormData({
        title: editingChangelog.title || "",
        description: editingChangelog.description || "",
        version: editingChangelog.version || "",
        publishDate: new Date(editingChangelog.publishDate),
        isPublished: editingChangelog.isPublished || false,
      });
    } else {
      resetForm();
    }
  }, [editingChangelog, isOpen]);


      console.error("Changelog update error:", error);
    },
  });

  const resetForm = () => {
    setFormData({
      title: "",
      description: "",
      version: "",
      publishDate: new Date(),
      isPublished: false,
    });
  };

  const handleSubmit = async () => {
    if (!formData.title || !formData.description) {
      toast.error("Please fill in all required fields");
      return;
    }

    setIsLoading(true);
    try {
      const publishDate = formData.publishDate
        ? new Date(formData.publishDate).getTime()
        : Date.now();

      if (isEditing && editingChangelog) {
        await updateChangelogMutation({
          id: editingChangelog._id,
          title: formData.title,
          description: formData.description,
          version: formData.version,
          publishDate: publishDate,
          isPublished: formData.isPublished,
          fixes: [],
          newFeatures: [],
        });
        toast.success("Changelog updated successfully!");
      } else {
        await createChangelogMutation({
          roadmapId: roadmapId as any,
          title: formData.title,
          description: formData.description,
          version: formData.version,
          publishDate: publishDate,
          isPublished: formData.isPublished,
          fixes: [],
          newFeatures: [],
        });
        toast.success("Changelog created successfully!");
      }

      resetForm();
      onClose();
    } catch (error) {
      console.error("Changelog operation error:", error);
      toast.error(isEditing ? "Failed to update changelog" : "Failed to create changelog");
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const [isLoading, setIsLoading] = useState(false);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Edit Changelog" : "Create Changelog"}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? "Update your changelog details"
              : "Create a new changelog entry for your roadmap"}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) =>
                  setFormData({ ...formData, title: e.target.value })
                }
                placeholder="June 2025 Updates"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="version">Version</Label>
              <Input
                id="version"
                value={formData.version}
                onChange={(e) =>
                  setFormData({ ...formData, version: e.target.value })
                }
                placeholder="1.2.0"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  description: e.target.value,
                })
              }
              placeholder="Describe the changes in this release"
              rows={4}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="publishDate">Publish Date</Label>
              <DateSelector
                value={formData.publishDate}
                onChange={(date) =>
                  setFormData({ ...formData, publishDate: date || new Date() })
                }
              />
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading
              ? isEditing
                ? "Updating..."
                : "Creating..."
              : isEditing
                ? "Update Changelog"
                : "Create Changelog"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
