{"name": "ray", "version": "0.0.1", "private": true, "scripts": {"build": "turbo build", "start": "turbo start", "dev": "turbo dev", "lint": "turbo run lint", "format": "turbo run format", "typecheck": "turbo run typecheck", "test": "turbo run test", "dedupe": "pnpm dedupe", "outdated": "pnpm outdated", "update": "pnpm update --recursive", "update-interactive": "pnpm update --recursive --interactive", "depcheck": "madge --circular --extensions ts,tsx,js,jsx ./apps ./packages", "health": "pnpm lint && pnpm typecheck && pnpm test && pnpm depcheck && pnpm dedupe && pnpm outdated"}, "devDependencies": {"@workspace/typescript-config": "workspace:*", "biome": "^0.3.3", "husky": "^9.1.7", "madge": "^8.0.0", "prettier": "^3.6.2", "turbo": "^2.5.5", "typescript": "5.8.3", "vitest": "^3.2.4"}, "packageManager": "pnpm@10.4.1", "engines": {"node": ">=20"}, "dependencies": {"jsdom": "^26.1.0", "require-in-the-middle": "^7.5.2", "react-hook-form": "^7.61.1"}}