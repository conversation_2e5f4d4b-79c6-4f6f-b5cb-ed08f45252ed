import { v } from "convex/values";
import { action, mutation, query } from "../_generated/server";
import { betterAuth } from "../auth";
import {
  FeatureRequestPriority,
  FeatureRequestStatus,
} from "../../schema/enum";
import { api } from "../_generated/api";

export const getFeatureRequestsByRoadmap = query({
  args: {
    roadmapId: v.id("PublicRoadmap"),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    return await ctx.db
      .query("FeatureRequest")
      .filter((q) => q.eq(q.field("roadmapId"), args.roadmapId))
      .collect();
  },
});

// Helper query for rate limiting
export const getRecentFeatureRequestsByIp = query({
  args: {
    ipAddress: v.string(),
    timeWindow: v.number(),
  },
  handler: async (ctx, args) => {
    const cutoffTime = Date.now() - args.timeWindow;
    return await ctx.db
      .query("FeatureRequest")
      .filter((q) => q.eq(q.field("ipAddress"), args.ipAddress))
      .filter((q) => q.gte(q.field("_creationTime"), cutoffTime))
      .collect();
  },
});

// Helper mutation for inserting feature requests
export const insertFeatureRequest = mutation({
  args: {
    roadmapId: v.id("PublicRoadmap"),
    title: v.string(),
    description: v.string(),
    category: v.string(),
    email: v.string(),
    name: v.optional(v.string()),
    ipAddress: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("FeatureRequest", {
      roadmapId: args.roadmapId,
      title: args.title,
      description: args.description,
      category: args.category,
      email: args.email,
      name: args.name,
      ipAddress: args.ipAddress,
      status: "pending",
      priority: "medium",
      isPublic: true,
    });
  },
});

export const createFeatureRequest = mutation({
  args: {
    roadmapId: v.id("PublicRoadmap"),
    title: v.string(),
    description: v.string(),
    category: v.string(),
    status: FeatureRequestStatus, // Assuming FeatureRequestStatus is a string
    priority: FeatureRequestPriority, // Assuming FeatureRequestPriority is a string
    requestedBy: v.optional(v.string()),
    contactEmail: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    return await ctx.db.insert("FeatureRequest", {
      roadmapId: args.roadmapId,
      title: args.title,
      description: args.description,
      category: args.category,
      status: args.status,
      priority: args.priority,
      email: args.contactEmail,
      ipAddress: "",
      isPublic: true,
    });
  },
});

export const updateFeatureRequest = mutation({
  args: {
    id: v.id("FeatureRequest"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    category: v.optional(v.string()),
    status: FeatureRequestStatus, // Assuming FeatureRequestStatus is a string
    priority: FeatureRequestPriority, // Assuming FeatureRequestPriority is a string
    email: v.optional(v.string()),
    isPublic: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }

    const { id, ...updates } = args;
    return await ctx.db.patch(id, updates);
  },
});

export const deleteFeatureRequest = mutation({
  args: {
    id: v.id("FeatureRequest"),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }

    const featureRequest = await ctx.db.get(args.id);
    if (!featureRequest) throw new Error("Feature request not found");

    // Verify the feature request belongs to a roadmap in the user's organization
    const roadmap = await ctx.db.get(featureRequest.roadmapId);
    if (!roadmap) throw new Error("Roadmap not found");

    const project = await ctx.db.get(roadmap.projectId);
    if (!project || project.organizationId !== identity.organizationId) {
      throw new Error("Unauthorized");
    }

    // Delete the feature request
    await ctx.db.delete(args.id);

    return { success: true };
  },
});

// Public version for landing page - no auth required
export const createPublicFeatureRequest = action({
  args: {
    roadmapId: v.id("PublicRoadmap"),
    title: v.string(),
    description: v.string(),
    category: v.string(),
    email: v.string(),
    name: v.optional(v.string()),
    ipAddress: v.string(),
  },
  handler: async (ctx, args) => {
    // Verify the roadmap exists and is public
    const roadmap = await ctx.runQuery(api.projects.roadmap.getRoadmapById, {
      id: args.roadmapId,
    });
    if (!roadmap || !roadmap.isPublic) {
      throw new Error("Roadmap not found or is not public");
    }

    // Rate limiting: Check if this IP has submitted feature requests recently
    const recentRequests = await ctx.runQuery(
      api.projects.featureRequest.getRecentFeatureRequestsByIp,
      {
        ipAddress: args.ipAddress,
        timeWindow: 60 * 60 * 1000, // 1 hour
      }
    );

    if (recentRequests.length >= 5) {
      throw new Error(
        "Rate limit exceeded. Please wait before submitting another feature request."
      );
    }

    await ctx.runMutation(api.projects.featureRequest.insertFeatureRequest, {
      roadmapId: args.roadmapId,
      title: args.title,
      description: args.description,
      category: args.category,
      email: args.email,
      name: args.name,
      ipAddress: args.ipAddress,
    });

    return {
      success: true,
    };
  },
});
