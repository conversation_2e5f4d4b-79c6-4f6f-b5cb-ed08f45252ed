"use client";

import { useQuery } from "convex/react";
import React, { useState } from "react";
import { api } from "@workspace/backend/convex/_generated/api";
import { FeatureRequestsTable } from "./feature-requests-table";
import { FeatureRequestSheet } from "./feature-request-sheet";
import { NoData } from "@/components/shared";

interface FeatureRequestsClientProps {
  roadmapId: string;
}

export function FeatureRequestsClient({
  roadmapId,
}: FeatureRequestsClientProps) {
  const [selectedRequest, setSelectedRequest] = useState<any>(null);
  const [isSheetOpen, setIsSheetOpen] = useState(false);

  // Fetch roadmap data using Convex
  const roadmap = useQuery(api.projects.roadmap.getRoadmapById, {
    id: roadmapId as any,
  });

  // Fetch feature requests using Convex
  const featureRequests = useQuery(api.projects.featureRequest.getFeatureRequestsByRoadmap, {
    roadmapId: roadmapId as any,
  });

  const handleViewRequest = (request: any) => {
    setSelectedRequest(request);
    setIsSheetOpen(true);
  };

  const handleCloseSheet = () => {
    setIsSheetOpen(false);
    setSelectedRequest(null);
  };

  // Show loading while data is being fetched
  if (roadmap === undefined || featureRequests === undefined) {
    return <div>Loading...</div>;
  }

  if (!roadmap) {
    return <NoData />;
  }

  return (
    <div>
      <FeatureRequestsTable
        featureRequests={featureRequests || []}
        onViewRequest={handleViewRequest}
        roadmapId={roadmapId}
      />

      <FeatureRequestSheet
        isOpen={isSheetOpen}
        onClose={handleCloseSheet}
        featureId={selectedRequest?.id}
        roadmapId={roadmapId}
        projectId={roadmap.projectId}
      />
    </div>
  );
}
