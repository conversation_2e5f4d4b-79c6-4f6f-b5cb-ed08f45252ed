import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { betterAuth } from "../auth";
import { ProjectPlatform, ProjectStatus } from "../../schema/enum";

// QUERIES

export const getAllProjects = query({
  args: {},
  handler: async (ctx) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const projects = await ctx.db
      .query("Project")
      .withIndex("by_organization", (q) =>
        q.eq("organizationId", session.organizationId)
      )
      .collect();

    return projects;
  },
});

export const getProjectById = query({
  args: { id: v.id("Project") },
  handler: async (ctx, { id }) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const project = await ctx.db.get(id);

    if (!project) {
      throw new Error("Project not found");
    }

    // Ensure user can only access projects from their organization
    if (project.organizationId !== session.organizationId) {
      throw new Error("Unauthorized access to project");
    }

    return project;
  },
});

export const getProjectsByStatus = query({
  args: { status: ProjectStatus },
  handler: async (ctx, { status }) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const projects = await ctx.db
      .query("Project")
      .withIndex("by_status", (q) => q.eq("status", status))
      .filter((q) => q.eq(q.field("organizationId"), session.organizationId))
      .collect();

    return projects;
  },
});

export const getProjectsByIdea = query({
  args: { ideaId: v.id("Idea") },
  handler: async (ctx, { ideaId }) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const projects = await ctx.db
      .query("Project")
      .withIndex("by_idea", (q) => q.eq("ideaId", ideaId))
      .filter((q) => q.eq(q.field("organizationId"), session.organizationId))
      .collect();

    return projects;
  },
});

export const getProjectsCreatedByUser = query({
  args: { userId: v.optional(v.id("User")) },
  handler: async (ctx, { userId }) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const targetUserId = userId || session.id;

    const projects = await ctx.db
      .query("Project")
      .withIndex("by_organization", (q) =>
        q.eq("organizationId", session.organizationId)
      )
      .filter((q) => q.eq(q.field("createdById"), targetUserId))
      .collect();

    return projects;
  },
});

// MUTATIONS

export const createProject = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    platform: ProjectPlatform,
    ai: v.optional(v.string()),
    orm: v.optional(v.string()),
    database: v.optional(v.string()),
    auth: v.optional(v.string()),
    framework: v.optional(v.string()),
    infrastructure: v.optional(v.string()),
    dueDate: v.optional(v.number()),
    status: v.optional(ProjectStatus),
    ideaId: v.optional(v.id("Idea")),
  },
  handler: async (
    ctx,
    {
      name,
      description,
      platform,
      ai,
      orm,
      database,
      auth,
      framework,
      infrastructure,
      dueDate,
      status,
      ideaId,
    }
  ) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    // Validate idea belongs to organization if provided
    if (ideaId) {
      const idea = await ctx.db.get(ideaId);
      if (!idea || idea.organizationId !== session.organizationId) {
        throw new Error("Invalid idea or unauthorized access");
      }
    }

    const projectId = await ctx.db.insert("Project", {
      name,
      description,
      platform,
      ai,
      orm,
      database,
      auth,
      framework,
      infrastructure,
      dueDate,
      status: status || "planning",
      ideaId,
      organizationId: session.organizationId,
      createdById: session.id,
    });

    return projectId;
  },
});

export const updateProject = mutation({
  args: {
    id: v.id("Project"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    platform: v.optional(ProjectPlatform),
    ai: v.optional(v.string()),
    orm: v.optional(v.string()),
    database: v.optional(v.string()),
    auth: v.optional(v.string()),
    framework: v.optional(v.string()),
    infrastructure: v.optional(v.string()),
    dueDate: v.optional(v.number()),
    status: v.optional(ProjectStatus),
    ideaId: v.optional(v.id("Idea")),
  },
  handler: async (
    ctx,
    {
      id,
      name,
      description,
      platform,
      ai,
      orm,
      database,
      auth,
      framework,
      infrastructure,
      dueDate,
      status,
      ideaId,
    }
  ) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const existingProject = await ctx.db.get(id);

    if (!existingProject) {
      throw new Error("Project not found");
    }

    // Ensure user can only update projects from their organization
    if (existingProject.organizationId !== session.organizationId) {
      throw new Error("Unauthorized access to project");
    }

    // Validate idea belongs to organization if provided
    if (ideaId) {
      const idea = await ctx.db.get(ideaId);
      if (!idea || idea.organizationId !== session.organizationId) {
        throw new Error("Invalid idea or unauthorized access");
      }
    }

    const updateData: any = {};
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (platform !== undefined) updateData.platform = platform;
    if (ai !== undefined) updateData.ai = ai;
    if (orm !== undefined) updateData.orm = orm;
    if (database !== undefined) updateData.database = database;
    if (auth !== undefined) updateData.auth = auth;
    if (framework !== undefined) updateData.framework = framework;
    if (infrastructure !== undefined)
      updateData.infrastructure = infrastructure;
    if (dueDate !== undefined) updateData.dueDate = dueDate;
    if (status !== undefined) updateData.status = status;
    if (ideaId !== undefined) updateData.ideaId = ideaId;

    await ctx.db.patch(id, updateData);

    return true;
  },
});

export const deleteProject = mutation({
  args: { id: v.id("Project") },
  handler: async (ctx, { id }) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const existingProject = await ctx.db.get(id);

    if (!existingProject) {
      throw new Error("Project not found");
    }

    // Ensure user can only delete projects from their organization
    if (existingProject.organizationId !== session.organizationId) {
      throw new Error("Unauthorized access to project");
    }

    // Check for related entities before deletion
    const relatedIssues = await ctx.db
      .query("Issue")
      .withIndex("by_project", (q) => q.eq("projectId", id))
      .first();

    const relatedFeatures = await ctx.db
      .query("Feature")
      .withIndex("by_project", (q) => q.eq("projectId", id))
      .first();

    const relatedAssets = await ctx.db
      .query("Asset")
      .withIndex("by_project", (q) => q.eq("projectId", id))
      .first();

    const relatedRoadmaps = await ctx.db
      .query("PublicRoadmap")
      .withIndex("byProject", (q) => q.eq("projectId", id))
      .first();

    if (relatedIssues || relatedFeatures || relatedAssets || relatedRoadmaps) {
      throw new Error(
        "Cannot delete project with existing issues, features, assets, or roadmaps. Please remove them first."
      );
    }

    await ctx.db.delete(id);

    return true;
  },
});

export const searchProjects = query({
  args: { searchTerm: v.string() },
  handler: async (ctx, { searchTerm }) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const projects = await ctx.db
      .query("Project")
      .withIndex("by_organization", (q) =>
        q.eq("organizationId", session.organizationId)
      )
      .filter((q) =>
        q.or(
          q.eq(q.field("name"), searchTerm),
          q.eq(q.field("description"), searchTerm)
        )
      )
      .collect();

    // Simple text search since Convex doesn't have full-text search
    const filteredProjects = projects.filter(
      (project) =>
        project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (project.description &&
          project.description.toLowerCase().includes(searchTerm.toLowerCase()))
    );

    return filteredProjects;
  },
});
