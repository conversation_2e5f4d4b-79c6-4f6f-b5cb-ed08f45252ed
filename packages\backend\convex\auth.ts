import {
  BetterAuth,
  type AuthFunctions,
  type PublicAuthFunctions,
} from "@convex-dev/better-auth";
import { api, components, internal } from "./_generated/api";
import { action, internalQuery, mutation, query } from "./_generated/server";
import type { Id, DataModel } from "./_generated/dataModel";
import { createAuth } from "../lib/auth";
import { v } from "convex/values";

const authFunctions: AuthFunctions = internal.auth;
const publicAuthFunctions: PublicAuthFunctions = api.auth;

export const betterAuth = new BetterAuth(components.betterAuth, {
  authFunctions,
  publicAuthFunctions,
});

export const {
  createUser,
  updateUser,
  deleteUser,
  createSession,
  isAuthenticated,
} = betterAuth.createAuthFunctions<DataModel>({
  // Must create a user and return the user id
  onCreateUser: async (ctx, user) => {
    return ctx.db.insert("User", {
      email: user.email,
      emailVerified: user.emailVerified,
      name: user.name,
      image: user.image || "",
      role: user.role || "user",
      twoFactorEnabled: user.twoFactorEnabled || false,
    });
  },

  // Delete the user when they are deleted from Better Auth
  onDeleteUser: async (ctx, userId) => {
    await ctx.db.delete(userId as Id<"User">);
  },

  onUpdateUser: async (ctx, user) => {
    await ctx.db.patch(user.userId as Id<"User">, {
      email: user.email,
      emailVerified: user.emailVerified,
      name: user.name,
      image: user.image || "",
      role: user.role || "user",
      twoFactorEnabled: user.twoFactorEnabled || false,
    });
  },
});

export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    // Get user data from Better Auth - email, name, image, etc.
    const userMetadata = await betterAuth.getAuthUser(ctx);
    if (!userMetadata) {
      return null;
    }
    // Get user data from your application's database
    // (skip this if you have no fields in your users table schema)
    const user = await ctx.db.get(userMetadata.userId as Id<"User">);
    return {
      ...user,
      ...userMetadata,
    };
  },
});

export const getFullSession = query({
  args: {},
  handler: async (ctx) => {
    const auth = createAuth(ctx);
    const headers = await betterAuth.getHeaders(ctx);
    const org = await auth.api.getFullOrganization({ headers });

    const session = await auth.api.getSession({ headers });

    return {
      ...session,
      org,
    };
  },
});

export const getSession = internalQuery({
  args: {},
  handler: async (ctx) => {
    const auth = createAuth(ctx);
    const headers = await betterAuth.getHeaders(ctx);
    const org = await auth.api.getFullOrganization({ headers });
    const session = await auth.api.getSession({ headers });

    if (!session?.session || !session?.user) {
      throw new Error("Not authenticated");
    }

    if (!org?.id || !org.name) {
      throw new Error("Not authenticated");
    }

    const me = org.members.find((m) => m.userId === session?.session.userId);

    if (!me) {
      throw new Error("Not authenticated");
    }

    return {
      userId: me.userId,
      org: org.id,
      email: me.user.email,
      name: me.user.name,
      image: me.user.image,
      role: me.role,
      orgName: org.name,
      memberId: me.id,
      token: session.session.token,
    };
  },
});

// INVITATIONS

export const getInvitations = query({
  handler: async (ctx) => {
    const auth = createAuth(ctx);
    const headers = await betterAuth.getHeaders(ctx);

    const allInvites = await auth.api.listUserInvitations({
      headers,
    });

    // Use Promise.all at the top level to parallelize all requests
    const invitations = await Promise.all(
      allInvites.map(async (invite) => {
        const org = await auth.api.getFullOrganization({
          query: {
            membersLimit: 1,
            organizationId: invite.organizationId,
          },
          headers,
        });

        return {
          ...invite,
          organizationName: org?.name,
        };
      })
    );

    return invitations;
  },
});

export const getInvitation = query({
  args: {
    id: v.string(),
  },
  handler: async (ctx, { id }) => {
    const auth = createAuth(ctx);
    const headers = await betterAuth.getHeaders(ctx);

    const invitation = await auth.api.getInvitation({
      query: {
        id,
      },
      headers,
    });

    return invitation;
  },
});

export const acceptInvitation = mutation({
  args: {
    id: v.string(),
  },
  handler: async (ctx, { id }) => {
    const auth = createAuth(ctx);
    const headers = await betterAuth.getHeaders(ctx);

    const data = await auth.api.acceptInvitation({
      body: {
        invitationId: id,
      },
      headers,
    });

    return data;
  },
});

export const rejectInvitation = mutation({
  args: {
    id: v.string(),
  },
  handler: async (ctx, { id }) => {
    const auth = createAuth(ctx);
    const headers = await betterAuth.getHeaders(ctx);

    const data = await auth.api.rejectInvitation({
      body: {
        invitationId: id,
      },
      headers,
    });

    return data;
  },
});

export const cancelInvitation = mutation({
  args: {
    id: v.string(),
  },
  handler: async (ctx, { id }) => {
    const auth = createAuth(ctx);
    const headers = await betterAuth.getHeaders(ctx);

    const data = await auth.api.cancelInvitation({
      body: {
        invitationId: id,
      },
      headers,
    });

    return data;
  },
});

export const verifyEmail = mutation({
  args: {
    token: v.string(),
  },
  handler: async (ctx, { token }) => {
    const auth = createAuth(ctx);
    const headers = await betterAuth.getHeaders(ctx);

    const data = await auth.api.verifyEmail({
      query: {
        token,
      },
      headers,
    });

    return data;
  },
});

export const getOrganizations = query({
  args: {},
  handler: async (ctx) => {
    const auth = createAuth(ctx);
    const headers = await betterAuth.getHeaders(ctx);

    const orgs = await auth.api.listOrganizations({
      headers,
    });

    return orgs;
  },
});

export const getOrgMembers = query({
  handler: async (ctx) => {
    const auth = createAuth(ctx);
    const headers = await betterAuth.getHeaders(ctx);

    const data = await auth.api.getFullOrganization({ headers });
    const members = data?.members || [];
    return members;
  },
});

export const getSubscription = query({
  args: {},
  handler: async (ctx) => {
    const auth = createAuth(ctx);
    const headers = await betterAuth.getHeaders(ctx);
    const org = await auth.api.getFullOrganization({ headers });

    if (!org?.id) {
      return null;
    }

    const subscription = await ctx.db
      .query("Subscription")
      .withIndex("by_organisation_id", (q) =>
        q.eq("organisation_id", org.id as any)
      )
      .first();

    return subscription;
  },
});
