import { getSession } from "@/actions/account/user";
import { SessionProvider } from "@/context/session-context";
import Appbar from "@/components/sidebar/app-bar";
import { LayoutContainer } from "@/components/layout-container";
import { LiveBlockProvider } from "@/components/liveblocks/provider";

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getSession();

  return (
    <SessionProvider sessionData={session}>
      <LiveBlockProvider>
        <Appbar>
          <LayoutContainer>
            <main className="scrollbar-hide">{children}</main>
          </LayoutContainer>
        </Appbar>
      </LiveBlockProvider>
    </SessionProvider>
  );
}
