import { Suspense } from "react";
import { notFound } from "next/navigation";
import { AssetsClient } from "./_components/assets-client";
import { AssetsSkeleton } from "./_components/assets-skeleton";

interface AssetsPageProps {
  params: Promise<{ id: string }>;
}

export default async function AssetsPage({ params }: AssetsPageProps) {
  const { id: projectId } = await params;

  if (!projectId) {
    notFound();
  }

  return (
    <Suspense fallback={<AssetsSkeleton />}>
      <AssetsClient projectId={projectId} />
    </Suspense>
  );
}
