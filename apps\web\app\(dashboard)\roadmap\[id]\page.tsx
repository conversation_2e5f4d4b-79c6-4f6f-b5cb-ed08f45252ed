import { Suspense } from "react";
import { RoadmapDetailClient } from "./_components/roadmap-detail-client";
import LoadingSpinner from "@workspace/ui/components/loading-spinner";

interface RoadmapDetailPageProps {
  params: Promise<{ id: string }>;
}

export default async function RoadmapDetailPage({
  params,
}: RoadmapDetailPageProps) {
  const { id: roadmapId } = await params;

  return (
    <Suspense fallback={<LoadingSpinner />}>
      <RoadmapDetailClient roadmapId={roadmapId} />
    </Suspense>
  );
}
