// @ts-nocheck -- skip type checking
import * as docs_5 from "../content/docs/waitlist/index.mdx?collection=docs&hash=1752194576864"
import * as docs_4 from "../content/docs/waitlist/hosted.mdx?collection=docs&hash=1752194576864"
import * as docs_3 from "../content/docs/waitlist/api.mdx?collection=docs&hash=1752194576864"
import * as docs_2 from "../content/docs/index.mdx?collection=docs&hash=1752194576864"
import * as docs_1 from "../content/docs/api.mdx?collection=docs&hash=1752194576864"
import * as docs_0 from "../content/docs/about.mdx?collection=docs&hash=1752194576864"
import { _runtime } from "fumadocs-mdx"
import * as _source from "../source.config"
export const docs = _runtime.docs<typeof _source.docs>([{ info: {"path":"about.mdx","absolutePath":"C:/Users/<USER>/Work/Web Apps/newray/apps/docs/content/docs/about.mdx"}, data: docs_0 }, { info: {"path":"api.mdx","absolutePath":"C:/Users/<USER>/Work/Web Apps/newray/apps/docs/content/docs/api.mdx"}, data: docs_1 }, { info: {"path":"index.mdx","absolutePath":"C:/Users/<USER>/Work/Web Apps/newray/apps/docs/content/docs/index.mdx"}, data: docs_2 }, { info: {"path":"waitlist\\api.mdx","absolutePath":"C:/Users/<USER>/Work/Web Apps/newray/apps/docs/content/docs/waitlist/api.mdx"}, data: docs_3 }, { info: {"path":"waitlist\\hosted.mdx","absolutePath":"C:/Users/<USER>/Work/Web Apps/newray/apps/docs/content/docs/waitlist/hosted.mdx"}, data: docs_4 }, { info: {"path":"waitlist\\index.mdx","absolutePath":"C:/Users/<USER>/Work/Web Apps/newray/apps/docs/content/docs/waitlist/index.mdx"}, data: docs_5 }], [])