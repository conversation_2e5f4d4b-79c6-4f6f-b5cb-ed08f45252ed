"use client";

import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@workspace/ui/components/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import { CheckIcon } from "lucide-react";
import { useId, useState } from "react";
import {
  projectTypes,
  ProjectType,
  ProjectPlatformType,
} from "@/utils/constants/projects/projectTypes"; // Import the project types

interface ProjectTypeSelectorProps {
  selectedType: ProjectPlatformType;
  onChange?: (type: ProjectPlatformType) => void;
  iconOnly?: boolean;
  size?: "xs" | "sm" | "lg";
  disabled?: boolean;
}

export function ProjectTypeSelector({
  selectedType,
  onChange,
  iconOnly = false,
  size = "xs",
  disabled = false,
}: ProjectTypeSelectorProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);

  const handleTypeChange = (typeId: ProjectPlatformType) => {
    onChange?.(typeId);
    setOpen(false);
  };

  const selectedProjectType: ProjectType | undefined = projectTypes.find(
    (type) => type.id === selectedType
  );

  return (
    <div className="*:not-first:mt-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={id}
            className="flex items-center justify-center"
            size={size}
            variant="secondary"
            role="combobox"
            aria-expanded={open}
            disabled={disabled}
          >
            {selectedProjectType ? (
              iconOnly ? (
                <selectedProjectType.icon className="text-muted-foreground size-4" />
              ) : (
                <div className="flex items-center gap-2">
                  <selectedProjectType.icon className="text-muted-foreground size-4" />
                  <span>{selectedProjectType.name}</span>
                </div>
              )
            ) : (
              <span>Select Project Type</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="border-input w-full min-w-[var(--radix-popper-anchor-width)] p-0"
          align="start"
        >
          <Command>
            <CommandInput placeholder="Search project types..." />
            <CommandList>
              <CommandEmpty>No project types found.</CommandEmpty>
              <CommandGroup>
                {projectTypes.map((type) => (
                  <CommandItem
                    key={type.id}
                    value={type.id}
                    onSelect={() =>
                      handleTypeChange(type.id as ProjectPlatformType)
                    }
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center gap-2">
                      {/* @ts-ignore */}
                      <type.icon className="text-muted-foreground size-4" />
                      <span>{type.name}</span>
                    </div>
                    {selectedType === type.id && (
                      <CheckIcon size={16} className="ml-auto" />
                    )}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
