import React, { Suspense } from "react";
import { IdeaDetailsSkeleton } from "@/components/idea/core/idea-details-skeleton";

interface IdeaPageProps {
  params: Promise<{ id: string }>;
}

const IdeaPage = async ({ params }: IdeaPageProps) => {
  const { id } = await params;

  return (
    <div className="flex flex-col h-full">
      <Suspense fallback={<IdeaDetailsSkeleton />}>
        <></>
      </Suspense>
    </div>
  );
};

export default IdeaPage;
