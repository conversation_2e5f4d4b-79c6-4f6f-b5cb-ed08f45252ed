# Backend Package - Services and Utilities

This package contains shared services and utilities used by both the `landing` and `web` applications.

## Services

### Demo Data Seeder

The demo data seeder service provides functionality to populate applications with sample data for development and testing purposes.

## Development

To run tests:
```bash
pnpm test
```

To check types:
```bash
pnpm typecheck
```

## Structure

```
packages/backend/
├── services/           # Shared services
├── lib/               # Utility libraries
├── convex/            # Convex backend configuration
└── schema/            # Data schemas and types
```