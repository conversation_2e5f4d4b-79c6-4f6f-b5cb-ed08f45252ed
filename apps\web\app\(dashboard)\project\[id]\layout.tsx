import { ReactNode } from "react";
import { Suspense } from "react";
import { ProjectLayoutClient } from "./_components/project-layout-client";
import LoadingSpinner from "@workspace/ui/components/loading-spinner";

interface ProjectLayoutProps {
  children: ReactNode;
  params: Promise<{ id: string }>;
}

export default async function ProjectLayout({
  children,
  params,
}: ProjectLayoutProps) {
  const { id } = await params;

  return (
    <Suspense fallback={<LoadingSpinner />}>
      <ProjectLayoutClient projectId={id}>
        {children}
      </ProjectLayoutClient>
    </Suspense>
  );
}
