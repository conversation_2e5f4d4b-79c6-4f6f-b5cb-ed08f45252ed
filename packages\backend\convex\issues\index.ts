import { v } from "convex/values";
import { query, mutation } from "../_generated/server";
import { betterAuth } from "../auth";
import { api, internal } from "../_generated/api";
import { Id } from "../_generated/dataModel";
import { IssueStatus, IssueLabel, Importance, ActivityType, EntityType } from "../../schema/enum";

export const getIssuesByProject = query({
  args: {
    projectId: v.id("Project"),
  },
  handler: async (ctx, { projectId }) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const issues = await ctx.db
      .query("Issue")
      .withIndex("by_project", (q) => q.eq("projectId", projectId))
      .collect();

    return issues.map((issue) => ({
      id: issue._id,
      title: issue.title,
      description: issue.description,
      status: issue.status,
      priority: issue.priority,
    }));
  },
});

export const listAllIssues = query({
  args: {},
  handler: async (ctx) => {
    const session = await betterAuth.getAuthUser(ctx);

    // TODO Fix the org session here and every where els
    console.log("SESSION", session);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const issues = await ctx.db
      .query("Issue")
      .withIndex("by_organization", (q) =>
        q.eq("organizationId", session.organizationId as any)
      )
      .collect();

    // Fetch related project and assigned user for each issue
    const results = await Promise.all(
      issues.map(async (issue) => {
        const project = issue.projectId ? await ctx.db.get(issue.projectId) : null;
        const assignedTo = issue.assignedToId ? await ctx.db.get(issue.assignedToId) : null;
        return {
          id: issue._id,
          description: issue.description,
          ...issue,
          project,
          assignedTo,
        };
      })
    );

    return results;
  },
});

// Get a single issue by ID
export const getIssue = query({
  args: { id: v.id("Issue") },
  handler: async (ctx, { id }) => {
    const session = await betterAuth.getAuthUser(ctx);
    if (!session) {
      throw new Error("Not authenticated");
    }

    const issue = await ctx.db.get(id);
    if (!issue || issue.organizationId !== session.organizationId) {
      return null;
    }

    // Fetch related data
    const project = issue.projectId ? await ctx.db.get(issue.projectId) : null;
    const assignedTo = issue.assignedToId ? await ctx.db.get(issue.assignedToId) : null;
    const milestone = issue.milestoneId ? await ctx.db.get(issue.milestoneId) : null;

    return {
      id: issue._id,
      ...issue,
      project,
      assignedTo,
      milestone,
    };
  },
});

// Create a new issue
export const createIssue = mutation({
  args: {
    title: v.string(),
    description: v.optional(v.string()),
    projectId: v.id("Project"),
    milestoneId: v.optional(v.id("Milestone")),
    featureId: v.optional(v.id("Feature")),
    parentIssueId: v.optional(v.id("Issue")),
    status: IssueStatus,
    priority: Importance,
    label: IssueLabel,
    dueDate: v.optional(v.number()),
    assignedToId: v.optional(v.id("User")),
    isPublic: v.optional(v.boolean()),
    sourceType: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const session = await betterAuth.getAuthUser(ctx);
    if (!session) {
      throw new Error("Not authenticated");
    }

    // Create the issue
    const issueId = await ctx.db.insert("Issue", {
      ...args,
      organizationId: session.organizationId as string,
      achieved: false,
    });

    // Create activity feed entry
    await ctx.db.insert("ActivityFeed", {
      type: "CREATED",
      title: `Issue "${args.title}" created`,
      description: "New issue created.",
      entityType: "ISSUE",
      entityId: issueId,
      organizationId: session.organizationId as string,
      userId: session.userId as Id<"User">,
    });

    return issueId;
  },
});

// Update an issue
export const updateIssue = mutation({
  args: {
    id: v.id("Issue"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    status: v.optional(IssueStatus),
    priority: v.optional(Importance),
    label: v.optional(IssueLabel),
    dueDate: v.optional(v.number()),
    assignedToId: v.optional(v.id("User")),
    milestoneId: v.optional(v.id("Milestone")),
    parentIssueId: v.optional(v.id("Issue")),
    achieved: v.optional(v.boolean()),
  },
  handler: async (ctx, { id, ...updates }) => {
    const session = await betterAuth.getAuthUser(ctx);
    if (!session) {
      throw new Error("Not authenticated");
    }

    // Get the original issue
    const originalIssue = await ctx.db.get(id);
    if (!originalIssue || originalIssue.organizationId !== session.organizationId) {
      throw new Error("Issue not found");
    }

    // Update the issue
    await ctx.db.patch(id, updates);

    // Track changes for activity feed
    const changes: string[] = [];

    // Check for status changes
    if (updates.status && updates.status !== originalIssue.status) {
      changes.push(`Status changed from ${originalIssue.status} to ${updates.status}`);
      await ctx.db.insert("ActivityFeed", {
        type: "UPDATED",
        title: `Issue "${originalIssue.title}" status updated`,
        description: `Status changed from ${originalIssue.status} to ${updates.status}`,
        entityType: "ISSUE",
        entityId: id,
        organizationId: session.organizationId as string,
        userId: session.userId as Id<"User">,
        oldValue: originalIssue.status,
        newValue: updates.status,
      });
    }

    // Check for priority changes
    if (updates.priority && updates.priority !== originalIssue.priority) {
      changes.push(`Priority changed from ${originalIssue.priority} to ${updates.priority}`);
      await ctx.db.insert("ActivityFeed", {
        type: "UPDATED",
        title: `Issue "${originalIssue.title}" priority updated`,
        description: `Priority changed from ${originalIssue.priority} to ${updates.priority}`,
        entityType: "ISSUE",
        entityId: id,
        organizationId: session.organizationId as string,
        userId: session.userId as Id<"User">,
        oldValue: originalIssue.priority,
        newValue: updates.priority,
      });
    }

    // Check for assignment changes
    if (updates.assignedToId !== undefined && updates.assignedToId !== originalIssue.assignedToId) {
      const activityType = updates.assignedToId ? "ASSIGNED" : "UNASSIGNED";
      await ctx.db.insert("ActivityFeed", {
        type: activityType,
        title: `Issue "${originalIssue.title}" ${updates.assignedToId ? "assigned" : "unassigned"}`,
        description: updates.assignedToId ? "Issue assigned to user" : "Issue unassigned",
        entityType: "ISSUE",
        entityId: id,
        organizationId: session.organizationId as string,
        userId: session.userId as Id<"User">,
        oldValue: originalIssue.assignedToId || "",
        newValue: updates.assignedToId || "",
      });
    }

    return id;
  },
});

// Delete an issue
export const deleteIssue = mutation({
  args: { id: v.id("Issue") },
  handler: async (ctx, { id }) => {
    const session = await betterAuth.getAuthUser(ctx);
    if (!session) {
      throw new Error("Not authenticated");
    }

    const issue = await ctx.db.get(id);
    if (!issue || issue.organizationId !== session.organizationId) {
      throw new Error("Issue not found");
    }

    await ctx.db.delete(id);

    // Create activity feed entry
    await ctx.db.insert("ActivityFeed", {
      type: "UPDATED",
      title: `Issue "${issue.title}" deleted`,
      description: "Issue was deleted",
      entityType: "ISSUE",
      entityId: id,
      organizationId: session.organizationId as string,
      userId: session.userId as Id<"User">,
    });

    return id;
  },
});

// Update issue label specifically
export const updateIssueLabel = mutation({
  args: {
    id: v.id("Issue"),
    label: IssueLabel,
  },
  handler: async (ctx, { id, label }) => {
    const session = await betterAuth.getAuthUser(ctx);
    if (!session) {
      throw new Error("Not authenticated");
    }

    const issue = await ctx.db.get(id);
    if (!issue || issue.organizationId !== session.organizationId) {
      throw new Error("Issue not found");
    }

    await ctx.db.patch(id, { label });

    return id;
  },
});

// Get issue hierarchy (parent and sub-issues)
export const getIssueHierarchy = query({
  args: { issueId: v.id("Issue") },
  handler: async (ctx, { issueId }) => {
    const session = await betterAuth.getAuthUser(ctx);
    if (!session) {
      throw new Error("Not authenticated");
    }

    const issue = await ctx.db.get(issueId);
    if (!issue || issue.organizationId !== session.organizationId) {
      return { parentIssue: null, subIssues: [] };
    }

    // Get parent issue (if any)
    let parentIssue = null;
    if (issue.parentIssueId) {
      const parent = await ctx.db.get(issue.parentIssueId);
      if (parent && parent.organizationId === session.organizationId) {
        const project = parent.projectId ? await ctx.db.get(parent.projectId) : null;
        const assignedTo = parent.assignedToId ? await ctx.db.get(parent.assignedToId) : null;
        parentIssue = {
          id: parent._id,
          ...parent,
          project,
          assignedTo,
        };
      }
    }

    // Get sub-issues
    const subIssues = await ctx.db
      .query("Issue")
      .withIndex("by_organization", (q) => q.eq("organizationId", session.organizationId as string))
      .filter((q) => q.eq(q.field("parentIssueId"), issueId))
      .collect();

    const subIssuesWithRelations = await Promise.all(
      subIssues.map(async (subIssue) => {
        const project = subIssue.projectId ? await ctx.db.get(subIssue.projectId) : null;
        const assignedTo = subIssue.assignedToId ? await ctx.db.get(subIssue.assignedToId) : null;
        return {
          id: subIssue._id,
          ...subIssue,
          project,
          assignedTo,
        };
      })
    );

    return { parentIssue, subIssues: subIssuesWithRelations };
  },
});

// Get issue dependencies and dependents
export const getIssueDependencies = query({
  args: { issueId: v.id("Issue") },
  handler: async (ctx, { issueId }) => {
    const session = await betterAuth.getAuthUser(ctx);
    if (!session) {
      throw new Error("Not authenticated");
    }

    // Get dependencies (issues this issue depends on)
    const dependencyRelations = await ctx.db
      .query("IssueDependency")
      .filter((q) =>
        q.and(
          q.eq(q.field("issueId"), issueId),
          q.eq(q.field("organizationId"), session.organizationId as string)
        )
      )
      .collect();

    const dependencies = await Promise.all(
      dependencyRelations.map(async (relation) => {
        const issue = await ctx.db.get(relation.dependencyId);
        if (!issue) return null;
        const project = issue.projectId ? await ctx.db.get(issue.projectId) : null;
        const assignedTo = issue.assignedToId ? await ctx.db.get(issue.assignedToId) : null;
        return {
          id: issue._id,
          ...issue,
          project,
          assignedTo,
        };
      })
    );

    // Get dependents (issues that depend on this issue)
    const dependentRelations = await ctx.db
      .query("IssueDependency")
      .filter((q) =>
        q.and(
          q.eq(q.field("dependencyId"), issueId),
          q.eq(q.field("organizationId"), session.organizationId as string)
        )
      )
      .collect();

    const dependents = await Promise.all(
      dependentRelations.map(async (relation) => {
        const issue = await ctx.db.get(relation.issueId);
        if (!issue) return null;
        const project = issue.projectId ? await ctx.db.get(issue.projectId) : null;
        const assignedTo = issue.assignedToId ? await ctx.db.get(issue.assignedToId) : null;
        return {
          id: issue._id,
          ...issue,
          project,
          assignedTo,
        };
      })
    );

    return {
      dependencies: dependencies.filter(Boolean),
      dependents: dependents.filter(Boolean)
    };
  },
});

// Get issue activity feed
export const getIssueActivity = query({
  args: {
    issueId: v.id("Issue"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, { issueId, limit = 50 }) => {
    const session = await betterAuth.getAuthUser(ctx);
    if (!session) {
      throw new Error("Not authenticated");
    }

    const activities = await ctx.db
      .query("ActivityFeed")
      .filter((q) =>
        q.and(
          q.eq(q.field("entityType"), "ISSUE"),
          q.eq(q.field("entityId"), issueId),
          q.eq(q.field("organizationId"), session.organizationId as string)
        )
      )
      .order("asc")
      .take(limit);

    // Fetch user details for each activity
    const activitiesWithUsers = await Promise.all(
      activities.map(async (activity) => {
        const user = activity.userId ? await ctx.db.get(activity.userId) : null;
        return {
          ...activity,
          user,
        };
      })
    );

    return activitiesWithUsers;
  },
});

// Validate if an issue can be completed
export const validateIssueCompletion = query({
  args: { issueId: v.id("Issue") },
  handler: async (ctx, { issueId }) => {
    const session = await betterAuth.getAuthUser(ctx);
    if (!session) {
      throw new Error("Not authenticated");
    }

    // Get dependencies (issues this issue depends on)
    const dependencyRelations = await ctx.db
      .query("IssueDependency")
      .filter((q) =>
        q.and(
          q.eq(q.field("issueId"), issueId),
          q.eq(q.field("organizationId"), session.organizationId as string)
        )
      )
      .collect();

    const dependencies = await Promise.all(
      dependencyRelations.map(async (relation) => {
        const issue = await ctx.db.get(relation.dependencyId);
        return issue;
      })
    );

    // Blockers: dependencies that are not DONE
    const blockers = dependencies.filter((dep) => dep && dep.status !== "DONE");

    return {
      canComplete: blockers.length === 0,
      blockers: blockers.filter(Boolean),
    };
  },
});

// Get all descendant issues (for preventing circular dependencies)
export const getAllDescendantIssues = query({
  args: { issueId: v.id("Issue") },
  handler: async (ctx, { issueId }) => {
    const session = await betterAuth.getAuthUser(ctx);
    if (!session) {
      throw new Error("Not authenticated");
    }

    const findDescendants = async (id: Id<"Issue">, acc: Set<string>): Promise<void> => {
      const dependentRelations = await ctx.db
        .query("IssueDependency")
        .filter((q) =>
          q.and(
            q.eq(q.field("dependencyId"), id),
            q.eq(q.field("organizationId"), session.organizationId as string)
          )
        )
        .collect();

      for (const relation of dependentRelations) {
        const childId = relation.issueId;
        if (!acc.has(childId)) {
          acc.add(childId);
          await findDescendants(childId, acc);
        }
      }
    };

    const acc = new Set<string>();
    await findDescendants(issueId, acc);
    return Array.from(acc);
  },
});

// Get all links for an issue
export const getIssueLinks = query({
  args: { issueId: v.id("Issue") },
  handler: async (ctx, { issueId }) => {
    const session = await betterAuth.getAuthUser(ctx);
    if (!session) {
      throw new Error("Not authenticated");
    }

    const links = await ctx.db
      .query("IssueLink")
      .filter((q) =>
        q.and(
          q.eq(q.field("issueId"), issueId),
          q.eq(q.field("organizationId"), session.organizationId as string)
        )
      )
      .collect();

    return links;
  },
});

// Add a link to an issue
export const addIssueLink = mutation({
  args: {
    issueId: v.id("Issue"),
    url: v.string(),
  },
  handler: async (ctx, { issueId, url }) => {
    const session = await betterAuth.getAuthUser(ctx);
    if (!session) {
      throw new Error("Not authenticated");
    }

    // Get issue details for activity tracking
    const issue = await ctx.db.get(issueId);
    if (!issue || issue.organizationId !== session.organizationId) {
      throw new Error("Issue not found");
    }

    const linkId = await ctx.db.insert("IssueLink", {
      issueId,
      url,
      organizationId: session.organizationId as string,
    });

    // Create activity feed entry
    await ctx.db.insert("ActivityFeed", {
      type: "LINK_ADDED",
      title: `Link added to "${issue.title}"`,
      description: `New link: ${url}`,
      entityType: "ISSUE",
      entityId: issueId,
      organizationId: session.organizationId as string,
      userId: session.userId as Id<"User">,
    });

    return linkId;
  },
});

// Delete a link from an issue
export const deleteIssueLink = mutation({
  args: { linkId: v.id("IssueLink") },
  handler: async (ctx, { linkId }) => {
    const session = await betterAuth.getAuthUser(ctx);
    if (!session) {
      throw new Error("Not authenticated");
    }

    // Get link details for activity tracking
    const link = await ctx.db.get(linkId);
    if (!link || link.organizationId !== session.organizationId) {
      throw new Error("Link not found");
    }

    const issue = await ctx.db.get(link.issueId);
    if (!issue) {
      throw new Error("Issue not found");
    }

    await ctx.db.delete(linkId);

    // Create activity feed entry
    await ctx.db.insert("ActivityFeed", {
      type: "LINK_REMOVED",
      title: `Link removed from "${issue.title}"`,
      description: `Link removed: ${link.url}`,
      entityType: "ISSUE",
      entityId: link.issueId,
      organizationId: session.organizationId as string,
      userId: session.userId as Id<"User">,
    });

    return linkId;
  },
});

// Add a dependency between two issues
export const addIssueDependency = mutation({
  args: {
    parentId: v.id("Issue"),
    dependentIssueId: v.id("Issue"),
  },
  handler: async (ctx, { parentId, dependentIssueId }) => {
    const session = await betterAuth.getAuthUser(ctx);
    if (!session) {
      throw new Error("Not authenticated");
    }

    // Prevent self-dependency
    if (parentId === dependentIssueId) {
      throw new Error("Cannot add self as dependency");
    }

    // Check if both issues exist and belong to the organization
    const parentIssue = await ctx.db.get(parentId);
    const dependentIssue = await ctx.db.get(dependentIssueId);

    if (!parentIssue || parentIssue.organizationId !== session.organizationId) {
      throw new Error("Parent issue not found");
    }
    if (!dependentIssue || dependentIssue.organizationId !== session.organizationId) {
      throw new Error("Dependent issue not found");
    }

    // Check if dependency already exists
    const existingDependency = await ctx.db
      .query("IssueDependency")
      .filter((q) =>
        q.and(
          q.eq(q.field("issueId"), dependentIssueId),
          q.eq(q.field("dependencyId"), parentId),
          q.eq(q.field("organizationId"), session.organizationId as string)
        )
      )
      .first();

    if (existingDependency) {
      throw new Error("Dependency already exists");
    }

    // Check for circular dependencies by getting all descendants of the parent
    const findDescendants = async (id: Id<"Issue">, acc: Set<string>): Promise<void> => {
      const dependentRelations = await ctx.db
        .query("IssueDependency")
        .filter((q) =>
          q.and(
            q.eq(q.field("dependencyId"), id),
            q.eq(q.field("organizationId"), session.organizationId as string)
          )
        )
        .collect();

      for (const relation of dependentRelations) {
        const childId = relation.issueId;
        if (!acc.has(childId)) {
          acc.add(childId);
          await findDescendants(childId, acc);
        }
      }
    };

    const descendants = new Set<string>();
    await findDescendants(parentId, descendants);
    if (descendants.has(dependentIssueId)) {
      throw new Error("Cannot add dependency: would create circular dependency");
    }

    // Add the dependency
    const dependencyId = await ctx.db.insert("IssueDependency", {
      issueId: dependentIssueId,
      dependencyId: parentId,
      organizationId: session.organizationId as string,
    });

    // Create activity feed entry
    await ctx.db.insert("ActivityFeed", {
      type: "DEPENDENCY_ADDED",
      title: `Dependency added to "${dependentIssue.title}"`,
      description: `Issue now depends on "${parentIssue.title}"`,
      entityType: "ISSUE",
      entityId: dependentIssueId,
      organizationId: session.organizationId as string,
      userId: session.userId as Id<"User">,
    });

    return dependencyId;
  },
});

// Remove a dependency between two issues
export const removeIssueDependency = mutation({
  args: {
    parentId: v.id("Issue"),
    dependentIssueId: v.id("Issue"),
  },
  handler: async (ctx, { parentId, dependentIssueId }) => {
    const session = await betterAuth.getAuthUser(ctx);
    if (!session) {
      throw new Error("Not authenticated");
    }

    // Get issue details for activity tracking
    const dependentIssue = await ctx.db.get(dependentIssueId);
    const parentIssue = await ctx.db.get(parentId);

    if (!dependentIssue || dependentIssue.organizationId !== session.organizationId) {
      throw new Error("Dependent issue not found");
    }
    if (!parentIssue || parentIssue.organizationId !== session.organizationId) {
      throw new Error("Parent issue not found");
    }

    // Find and delete the dependency
    const dependency = await ctx.db
      .query("IssueDependency")
      .filter((q) =>
        q.and(
          q.eq(q.field("issueId"), dependentIssueId),
          q.eq(q.field("dependencyId"), parentId),
          q.eq(q.field("organizationId"), session.organizationId as string)
        )
      )
      .first();

    if (!dependency) {
      throw new Error("Dependency not found");
    }

    await ctx.db.delete(dependency._id);

    // Create activity feed entry
    await ctx.db.insert("ActivityFeed", {
      type: "DEPENDENCY_REMOVED",
      title: `Dependency removed from "${dependentIssue.title}"`,
      description: `Issue no longer depends on "${parentIssue.title}"`,
      entityType: "ISSUE",
      entityId: dependentIssueId,
      organizationId: session.organizationId as string,
      userId: session.userId as Id<"User">,
    });

    return dependency._id;
  },
});
