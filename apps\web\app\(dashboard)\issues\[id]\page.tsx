"use client";
import { ExpandedLayoutContainer } from "@/components/expanded-layout-container";
import React from "react";
import IssueSidebar from "./components/issue-sidebar";
import { redirect } from "next/navigation";
import IssueDetails from "./components/issue-details";
import Header from "@/components/shared/header";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import LoadingSpinner from "@workspace/ui/components/loading-spinner";
import { NoData } from "@/components/shared";

const SingleIssuePage = ({
  params,
}: {
  params: { id: string };
}) => {
  const id = params.id as Id<"Issue">;

  // Use Convex query to fetch issue data
  const issue = useQuery(api.issues.index.getIssue, { id });

  // Handle loading state
  if (issue === undefined) {
    return (
      <div className="flex items-center justify-center p-20">
        <LoadingSpinner />
      </div>
    );
  }

  // Handle not found
  if (issue === null) {
    redirect("/issues");
    return null;
  }

  return (
    <>
      <Header
        crumb={[{ title: "Issues", url: "/issues" }, { title: "Manage issue" }]}
      >
        {null}
      </Header>
      <ExpandedLayoutContainer
        sidebar={<IssueSidebar issueId={id} />}
      >
        <div className="p-4">
          <IssueDetails id={id} />
        </div>
      </ExpandedLayoutContainer>
    </>
  );
};

export default SingleIssuePage;
