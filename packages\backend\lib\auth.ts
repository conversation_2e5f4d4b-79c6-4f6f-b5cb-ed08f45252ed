import { convexAdapter } from "@convex-dev/better-auth";
import { convex } from "@convex-dev/better-auth/plugins";
import { betterAuth as betterAuthInit } from "better-auth";
import { betterAuth } from "../convex/auth";
import { type GenericCtx } from "../convex/_generated/server";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { organization, twoFactor, magicLink } from "better-auth/plugins";
import { nextCookies } from "better-auth/next-js";
import { passkey } from "better-auth/plugins/passkey";
import { Polar } from "@polar-sh/sdk";
import { polar, portal, webhooks, checkout } from "@polar-sh/better-auth";
import { loops } from "./loops";
import { api, internal } from "../convex/_generated/api";

// You'll want to replace this with an environment variable
const siteUrl = process.env.NEXT_PUBLIC_AUTH_URL;

export const polarClient = new Polar({
  accessToken: process.env.POLAR_TOKEN,
  server: process.env.NODE_ENV === "production" ? "production" : "sandbox",
});

export const createAuth = (ctx: GenericCtx) =>
  // Configure your Better Auth instance here
  betterAuthInit({
    baseURL: siteUrl,
    database: convexAdapter(ctx, betterAuth),
    emailAndPassword: {
      enabled: true,
      requireEmailVerification: false,
    },
    plugins: [
      nextCookies(),
      polar({
        client: polarClient,
        createCustomerOnSignUp: process.env.NODE_ENV === "production",
        use: [
          portal(),
          checkout({
            authenticatedUsersOnly: true,
            successUrl: "/dashboard",
          }),
          webhooks({
            secret: process.env.POLAR_WEBHOOK_SECRET || "", // We need to enable webhooks on Polar as well
            onPayload: async ({ data, type }) => {
              if (
                type === "subscription.created" ||
                type === "subscription.active" ||
                type === "subscription.canceled" ||
                type === "subscription.revoked" ||
                type === "subscription.uncanceled" ||
                type === "subscription.updated"
              ) {
                // Call the HTTP endpoint to sync subscription
                try {
                  const response = await fetch(
                    `${process.env.CONVEX_SITE_URL}/sync-subscription`,
                    {
                      method: "POST",
                      headers: {
                        "Content-Type": "application/json",
                      },
                      body: JSON.stringify({
                        org: data.metadata.org as string,
                        productId: data.productId,
                        userId: data.customer.externalId,
                        status: data.status,
                        subscription_id: data.id,
                      }),
                    }
                  );

                  if (!response.ok) {
                    console.error(
                      "Failed to sync subscription:",
                      await response.text()
                    );
                  }
                } catch (error) {
                  console.error(
                    "Error calling sync subscription endpoint:",
                    error
                  );
                }
              }
            },
          }),
        ],
      }),
      organization({
        allowUserToCreateOrganization: true,
        membershipLimit: 50,
        sendInvitationEmail: async ({
          organization,
          email,
          role,
          inviter,
          invitation,
        }) => {
          await loops.sendTransactionalEmail({
            transactionalId: "cmdx3e2yl4iae070ifr1vlsmz",
            email,
            dataVariables: {
              org: organization.name,
              inviter: inviter.user.name,
              role,
              url: `${process.env.NEXT_PUBLIC_AUTH_URL}/auth/join-org/${invitation.id}`,
            },
          });
        },
      }),
      passkey(),
      twoFactor({
        issuer: "RayAI",
        otpOptions: {
          sendOTP: async ({ user, otp }) => {
            await loops.sendTransactionalEmail({
              transactionalId: "cmdx32i1t006kyp0ihrz8zk6i",
              email: user.email,
              dataVariables: {
                code: otp,
              },
            });
          },
        },
      }),
      magicLink({
        sendMagicLink: async ({ email, url }) => {
          await loops.sendTransactionalEmail({
            email,
            transactionalId: "cmdx2nsql1ik1wx0i5ogafg28",
            dataVariables: {
              url,
            },
          });
        },
      }),
      convex(),
    ],
  });
