"use client";

import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { Badge } from "@workspace/ui/components/badge";
import Link from "next/link";
import React from "react";
import { Button } from "@workspace/ui/components/button";
import { ExternalLink } from "lucide-react";
import { RoadmapTabs } from "./tabs";
import { RoadmapForm } from "../../components/new-roadmap";
import Header from "@/components/shared/header";
import { redirect } from "next/navigation";

interface RoadmapLayoutClientProps {
  roadmapId: string;
  children: React.ReactNode;
}

export function RoadmapLayoutClient({ roadmapId, children }: RoadmapLayoutClientProps) {
  const roadmap = useQuery(api.projects.roadmap.getRoadmapById, {
    id: roadmapId as Id<"PublicRoadmap">,
  });

  // Handle loading state
  if (roadmap === undefined) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Handle not found
  if (roadmap === null) {
    redirect("/roadmap");
  }

  return (
    <>
      <Header
        crumb={[
          { title: "Roadmaps", url: "/roadmap" },
          { title: roadmap?.name },
        ]}
      >
        <div className="flex items-center gap-2">
          <Button asChild size="icon">
            <Link
              href={`https://rayai.dev/rm/${roadmap?.slug}`}
              target="_blank"
            >
              <ExternalLink className="w-4 h-4" />
            </Link>
          </Button>
          <RoadmapForm mode="edit" roadmap={roadmap} />
        </div>
      </Header>
      <div className="space-y-2 container">
        <div className="flex items-center p-4 gap-4 flex-wrap justify-between">
          <div className="flex items-center gap-4">
            <div>
              <div className="flex items-center gap-2 mb-1">
                <Badge variant={roadmap?.isPublic ? "neutral" : "dark"}>
                  {roadmap?.isPublic ? "Public" : "Private"}
                </Badge>
                <h1 className="text-xl font-semibold">{roadmap?.name}</h1>
              </div>
              <p className="text-sm text-muted-foreground">
                {roadmap?.description}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2"></div>
        </div>
        <>
          <RoadmapTabs roadmapId={roadmapId} />
          <div className="p-4">{children}</div>
        </>
      </div>
    </>
  );
}
