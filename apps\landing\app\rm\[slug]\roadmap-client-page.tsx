"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { RoadmapHeader } from "./components/roadmap-header";
import { RoadmapContent } from "./components/roadmap-content";
import { api } from "@workspace/backend";
import { useQuery, useMutation } from "convex/react";

// Define types for roadmap items to match the actual schema
interface RoadmapItem {
  _id: string;
  title: string;
  description: string;
  status: string;
  category: string;
  priority: string;
  targetDate?: number;
  voteCount?: number;
  feedbackCount?: number;
}

// Define types for changelogs to match RoadmapContent expectations
interface ChangelogEntry {
  id: string;
  type: string;
  title: string;
  description?: string;
  priority?: string;
  category?: string;
  breaking?: boolean;
  issue?: any;
  feature?: any;
}

interface ChangelogItem {
  title: string;
  description: string;
  status: string;
}

interface Changelog {
  id: string;
  title: string;
  description: string;
  version?: string;
  publishDate: number;
  entries: ChangelogEntry[];
  items: ChangelogItem[];
}

export function RoadmapClientPage({ slug }: { slug: string }) {
  const roadmap = useQuery(api.projects.roadmap.getRoadmapBySlug, {
    slug,
  });

  if (roadmap === undefined) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (roadmap === null) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-2">Roadmap Not Found</h1>
          <p className="text-muted-foreground">The roadmap you're looking for doesn't exist.</p>
        </div>
      </div>
    );
  }

  const [activeTab, setActiveTab] = useState("kanban");
  const [searchQuery, setSearchQuery] = useState("");
  const [filterCategory, setFilterCategory] = useState("");
  const [filterStatus, setFilterStatus] = useState("");
  const [filterPriority, setFilterPriority] = useState("");
  const [feedbackContent, setFeedbackContent] = useState("");
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const [userIp, setUserIp] = useState<string>("");

  // Fetch roadmap items using public API
  const allItems = useQuery(
    api.projects.roadmapItem.getPublicRoadmapItemsByRoadmap,
    {
      roadmapId: roadmap._id,
    }
  );

  // Fetch changelogs using public API
  const changelogsData = useQuery(
    api.projects.roadmapChangelog.getPublicRoadmapChangelogsByRoadmap,
    {
      roadmapId: roadmap._id,
    }
  );

  // Transform changelogs to match expected interface
  const changelogs: Changelog[] = (changelogsData || []).map((changelog: any) => ({
    id: changelog._id,
    title: changelog.title,
    description: changelog.description,
    version: changelog.version,
    publishDate: changelog.publishDate,
    entries: changelog.entries || [],
    // Legacy support
    items: [
      ...(changelog.newFeatures || []).map((feature: string) => ({
        title: feature,
        description: `New feature: ${feature}`,
        status: "NEW",
      })),
      ...(changelog.fixes || []).map((fix: string) => ({
        title: fix,
        description: `Bug fix: ${fix}`,
        status: "FIXED",
      })),
    ],
  }));

  // Mutations
  const voteForItem = useMutation(api.projects.roadmapVote.createRoadmapVote);
  const addFeedback = useMutation(api.projects.roadmapFeedback.createRoadmapFeedback);

  // Get user IP address for voting
  useEffect(() => {
    const getIp = async () => {
      try {
        const response = await fetch("https://api.ipify.org?format=json");
        const data = await response.json();
        setUserIp(data.ip);
      } catch (error) {
        console.error("Failed to get IP address:", error);
        setUserIp("unknown");
      }
    };

    getIp();
  }, []);

  // Transform items to match expected interface
  const transformItem = (item: RoadmapItem) => ({
    id: item._id,
    title: item.title,
    description: item.description,
    status: item.status,
    category: item.category,
    priority: item.priority,
    voteCount: item.voteCount || 0,
    feedbackCount: item.feedbackCount || 0,
    targetDate: item.targetDate,
    updatedAt: Date.now(),
  });

  // Filter and sort items
  const filteredItems = (allItems || []).filter((item: RoadmapItem) => {
    const matchesSearch =
      searchQuery === "" ||
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.description.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesCategory =
      filterCategory === "" || item.category === filterCategory;
    const matchesStatus = filterStatus === "" || item.status === filterStatus;
    const matchesPriority =
      filterPriority === "" || item.priority === filterPriority;

    return matchesSearch && matchesCategory && matchesStatus && matchesPriority;
  });

  // Get unique categories and statuses for filters
  const categories = [...new Set(allItems?.map((item: RoadmapItem) => item.category) || [])] as string[];
  const statuses = [...new Set(allItems?.map((item: RoadmapItem) => item.status) || [])] as string[];

  // Group items by status for kanban view
  const itemsByStatus = {
    IN_REVIEW: filteredItems
      .filter((item: RoadmapItem) => item.status === "IN_REVIEW")
      .map(transformItem),
    IN_PROGRESS: filteredItems
      .filter((item: RoadmapItem) => item.status === "IN_PROGRESS")
      .map(transformItem),
    DONE: filteredItems
      .filter((item: RoadmapItem) => item.status === "DONE")
      .map(transformItem),
    CANCELLED: filteredItems
      .filter((item: RoadmapItem) => item.status === "CANCELLED")
      .map(transformItem),
    BLOCKED: filteredItems
      .filter((item: RoadmapItem) => item.status === "BLOCKED")
      .map(transformItem),
    BACKLOG: filteredItems
      .filter((item: RoadmapItem) => item.status === "BACKLOG")
      .map(transformItem),
  };

  // Handle voting
  const handleVote = async (itemId: string) => {
    if (!roadmap?.allowVoting) {
      toast.error("Voting is not enabled for this roadmap");
      return;
    }

    if (!userIp) {
      toast.error("Unable to determine your IP address for voting");
      return;
    }

    try {
      await voteForItem({
        roadmapItemId: itemId as any, // Type assertion for Convex ID
        ipAddress: userIp,
      });
      toast.success("Vote recorded!");
    } catch (error: any) {
      toast.error(error.message || "Failed to record vote");
    }
  };

  // Handle submitting feedback
  const handleSubmitFeedback = async () => {
    if (!roadmap?.allowFeedback) {
      toast.error("Feedback is not enabled for this roadmap");
      return;
    }

    if (!selectedItemId || !feedbackContent.trim() || !userIp) {
      toast.error("Please provide feedback content");
      return;
    }

    try {
      await addFeedback({
        roadmapItemId: selectedItemId as any, // Type assertion for Convex ID
        ipAddress: userIp,
        content: feedbackContent,
        sentiment: "neutral", // Default sentiment, will be analyzed in background
        isApproved: false, // Default to false, will be reviewed by admin
      });
      toast.success("Feedback submitted!");
      setFeedbackContent("");
      setSelectedItemId(null);
      setIsFeedbackModalOpen(false);
    } catch (error: any) {
      toast.error(error.message || "Failed to submit feedback");
    }
  };

  return (
    <>
      <RoadmapHeader 
        roadmap={{
          id: roadmap._id,
          name: roadmap.name,
          description: roadmap.description,
        }} 
        categories={categories} 
      />

      <div className="container mx-auto px-4 py-3">
        <RoadmapContent
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          changelogs={changelogs}
          itemsByStatus={itemsByStatus}
          selectedItemId={selectedItemId}
          setSelectedItemId={setSelectedItemId}
          feedbackContent={feedbackContent}
          setFeedbackContent={setFeedbackContent}
          handleVote={handleVote}
          handleSubmitFeedback={handleSubmitFeedback}
          isFeedbackModalOpen={isFeedbackModalOpen}
          setIsFeedbackModalOpen={setIsFeedbackModalOpen}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          filterCategory={filterCategory}
          setFilterCategory={setFilterCategory}
          filterStatus={filterStatus}
          setFilterStatus={setFilterStatus}
          filterPriority={filterPriority}
          setFilterPriority={setFilterPriority}
          roadmapName={roadmap.name}
          roadmapId={roadmap._id}
          categories={categories}
          statuses={statuses}
        />
      </div>
    </>
  );
}
