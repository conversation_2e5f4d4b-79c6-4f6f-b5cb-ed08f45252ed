"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Textarea } from "@workspace/ui/components/textarea";
import { Label } from "@workspace/ui/components/label";
import { Switch } from "@workspace/ui/components/switch";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";

import { toast } from "sonner";

interface EditRoadmapDialogProps {
  isOpen: boolean;
  onClose: () => void;
  roadmapId: string;
  roadmap?: {
    name: string;
    slug: string;
    description: string;
    isPublic: boolean;
    allowVoting: boolean;
    allowFeedback: boolean;
    project: string;
  };
}

export function EditRoadmapDialog({
  isOpen,
  onClose,
  roadmapId,
  roadmap,
}: EditRoadmapDialogProps) {
  const router = useRouter();
  const updateRoadmapMutation = useMutation(api.projects.roadmap.updateRoadmap);

  const [formData, setFormData] = useState({
    name: "",
    slug: "",
    description: "",
    isPublic: true,
    allowVoting: true,
    allowFeedback: true,
    projectId: "",
  });

  // Initialize form data when roadmap is loaded
  useEffect(() => {
    if (roadmap) {
      setFormData({
        name: roadmap.name,
        slug: roadmap.slug,
        description: roadmap.description,
        isPublic: roadmap.isPublic,
        allowVoting: roadmap.allowVoting,
        allowFeedback: roadmap.allowFeedback,
        projectId: roadmap.project,
      });
    }
  }, [roadmap]);

  const handleSubmit = async () => {
    if (!formData.name || !formData.slug || !formData.description) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      await updateRoadmapMutation({
        id: roadmapId as any,
        name: formData.name,
        slug: formData.slug,
        description: formData.description,
      });

      toast.success("Roadmap updated successfully!");
      onClose();
    } catch (error) {
      toast.error("Failed to update roadmap");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Edit Roadmap</DialogTitle>
          <DialogDescription>Update your roadmap settings</DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          <div className="space-y-2">
            <Label htmlFor="name">Roadmap Name *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  name: e.target.value,
                })
              }
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="slug">URL Slug *</Label>
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground">/roadmap/</span>
              <Input
                id="slug"
                value={formData.slug}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    slug: e.target.value,
                  })
                }
                className="flex-1"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  description: e.target.value,
                })
              }
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="isPublic"
                checked={formData.isPublic}
                onCheckedChange={(checked) =>
                  setFormData({
                    ...formData,
                    isPublic: checked,
                  })
                }
              />
              <Label htmlFor="isPublic">Public Roadmap</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="allowVoting"
                checked={formData.allowVoting}
                onCheckedChange={(checked) =>
                  setFormData({
                    ...formData,
                    allowVoting: checked,
                  })
                }
              />
              <Label htmlFor="allowVoting">Allow Voting</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="allowFeedback"
                checked={formData.allowFeedback}
                onCheckedChange={(checked) =>
                  setFormData({
                    ...formData,
                    allowFeedback: checked,
                  })
                }
              />
              <Label htmlFor="allowFeedback">Allow Feedback</Label>
            </div>
          </div>
        </div>

        <div className="flex items-center justify-end gap-2 py-2.5 px-4 w-full border-t">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>Save Changes</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
