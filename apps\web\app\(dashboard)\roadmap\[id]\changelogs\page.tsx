import { Suspense } from "react";
import { ChangelogsClient } from "./_components/changelogs-client";
import LoadingSpinner from "@workspace/ui/components/loading-spinner";

interface ChangelogsPageProps {
  params: Promise<{ id: string }>;
}

export default async function ChangelogsPage({ params }: ChangelogsPageProps) {
  const { id } = await params;

  return (
    <Suspense fallback={<LoadingSpinner />}>
      <ChangelogsClient roadmapId={id} />
    </Suspense>
  );
}
