import { ExpandedLayoutContainer } from "@/components/expanded-layout-container";
import FeatureSidebar from "./components/feature-sidebar";
import FeatureDetails from "./components/feature-details";
import Header from "@/components/shared/header";

const SingleFeaturePage = async ({
  params,
}: {
  params: Promise<{ id: string }>;
}) => {
  const { id } = await params;

  return (
    <div>
      <Header
        crumb={[
          { title: "Projects", url: "/project" },
          { title: "Feature", url: `/features/${id}` },
        ]}
      >
        {null}
      </Header>
      <ExpandedLayoutContainer sidebar={<FeatureSidebar featureId={id} />}>
        <div className="py-4 px-6">
          <FeatureDetails id={id} />
        </div>
      </ExpandedLayoutContainer>
    </div>
  );
};

export default SingleFeaturePage;
