"use server";
import { api } from "@workspace/backend";
import { fetchQuery } from "convex/nextjs";
import { Feature, GeneralFeature } from "@/types/features";
import { ConvexSubscription, convertConvexSubscription, Subscription } from "@/types/subscription";

export async function getSubscription(): Promise<ConvexSubscription | null> {
  try {
    const subscription = await fetchQuery(api.auth.getSubscription);
    return subscription;
  } catch (error) {
    console.error("Error fetching subscription:", error);
    return null;
  }
}

export async function getSubscriptionForUI(): Promise<Subscription | null> {
  try {
    const convexSubscription = await getSubscription();
    return convertConvexSubscription(convexSubscription);
  } catch (error) {
    console.error("Error fetching subscription for UI:", error);
    return null;
  }
}

export async function getAllowedFeatures(): Promise<Feature[]> {
  try {
    const subscription = await getSubscription();
    
    // If no subscription or not active, return empty array
    if (!subscription || subscription.status !== "active") {
      return [];
    }

    // Define features based on subscription/product_id
    // You can customize this logic based on your pricing tiers
    const productId = subscription.product_id;
    
    // Default features for all active subscriptions
    const baseFeatures: Feature[] = [
      GeneralFeature.Inbox,
      GeneralFeature.Feedback,
    ];

    // Add premium features based on product ID
    if (productId?.includes("pro") || productId?.includes("premium")) {
      baseFeatures.push(
        GeneralFeature.Agent,
        GeneralFeature.Analytics,
        GeneralFeature.Integration
      );
    }

    return baseFeatures;
  } catch (error) {
    console.error("Error fetching allowed features:", error);
    return [];
  }
}

export async function generateCustomerPortalURL(): Promise<string> {
  try {
    // This would typically call your billing provider's API
    // For now, we'll return a placeholder URL
    // You can implement this based on your billing provider (Stripe, Polar, etc.)
    
    // Example for Polar:
    // const response = await fetch('/api/billing/portal', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({ customerId: subscription.customerId })
    // });
    // const { url } = await response.json();
    // return url;
    
    // For now, return checkout page as fallback
    return `${process.env.NEXT_PUBLIC_APP_URL}/checkout`;
  } catch (error) {
    console.error("Error generating customer portal URL:", error);
    throw new Error("Failed to generate customer portal URL");
  }
} 