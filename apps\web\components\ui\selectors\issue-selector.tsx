"use client";

import { useId, useState } from "react";
import { Bug, CheckIcon } from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@workspace/ui/components/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import { projectTypes } from "@/utils/constants/projects/projectTypes";
import { TbListDetails } from "react-icons/tb";
import { useQuery } from "convex/react";
import { api, Id } from "@workspace/backend";

interface IssueSelectorProps {
  projectId?: Id<"Project">;
  value: string;
  onValueChange: (value: string) => void;
  excludeIssueId?: string | string[];
  placeholder?: string;
  // Legacy props for backward compatibility
  currentIssue?: string | null;
  onChange?: (issue: string | null) => void;
}

interface Issue {
  id: string;
  title: string;
}

export function IssueSelector({
  projectId,
  value,
  onValueChange,
  excludeIssueId,
  placeholder = "Select issue...",
  // Legacy props
  currentIssue,
  onChange,
}: IssueSelectorProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);

  // Use new props if available, otherwise fall back to legacy props
  const selectedValue = value !== undefined ? value : currentIssue || "";
  const handleChange = onValueChange || onChange;

  const handleIssueChange = (issueId: string) => {
    handleChange?.(issueId);
    setOpen(false);
  };

  const projectIssues = useQuery(
    api.issues.index.getIssuesByProject,
    projectId ? { projectId } : "skip"
  );

  const allIssues = useQuery(
    api.issues.index.listAllIssues,
    !projectId ? {} : "skip"
  );

  const issues = projectId ? projectIssues : allIssues;

  // Filter out excluded issues - support both single ID and array of IDs
  const excludedIds = Array.isArray(excludeIssueId)
    ? excludeIssueId
    : excludeIssueId
      ? [excludeIssueId]
      : [];

  const filteredIssues = (issues ?? []).filter(
    (issue: Issue) => !excludedIds.includes(issue.id)
  );

  const selectedIssue = filteredIssues.find(
    (issue: Issue) => issue.id === selectedValue
  );

  return (
    <div className="*:not-first:mt-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={id}
            className="flex items-center justify-center"
            size="sm"
            variant="secondary"
            role="combobox"
            aria-expanded={open}
            disabled={issues === undefined}
          >
            {issues === undefined ? (
              <div className="flex items-center gap-2">
                <div className="size-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                <span>Loading...</span>
              </div>
            ) : selectedIssue ? (
              <div className="flex items-center gap-2">
                <Bug className="text-muted-foreground size-4" />
                <span>{selectedIssue.title}</span>
              </div>
            ) : (
              <span>{placeholder}</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="border-input w-full min-w-[var(--radix-popper-anchor-width)] p-0"
          align="start"
        >
          <Command>
            <CommandInput placeholder="Search issues..." />
            <CommandList>
              <CommandEmpty>
                {issues === undefined ? (
                  <div className="flex items-center justify-center py-6">
                    <div className="size-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    <span className="ml-2">Loading issues...</span>
                  </div>
                ) : (
                  "No issues found."
                )}
              </CommandEmpty>
              <CommandGroup>
                <CommandItem
                  value=""
                  onSelect={() => handleIssueChange("")}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center gap-2">
                    <span className="text-muted-foreground">
                      No issue selected
                    </span>
                  </div>
                  {!selectedValue && (
                    <CheckIcon size={16} className="ml-auto" />
                  )}
                </CommandItem>
                {filteredIssues.map((issue: Issue) => {
                  const issueType = projectTypes.find(
                    (type) => type.id === issue.title
                  );
                  return (
                    <CommandItem
                      key={issue.id}
                      value={issue.id}
                      onSelect={() => handleIssueChange(issue.id)}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-2">
                        {issueType ? (
                          <issueType.icon
                            className={issueType.colorClass + " size-4"}
                          />
                        ) : (
                          <TbListDetails className="text-muted-foreground size-4" />
                        )}
                        <span>{issue.title}</span>
                      </div>
                      {selectedValue === issue.id && (
                        <CheckIcon size={16} className="ml-auto" />
                      )}
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
