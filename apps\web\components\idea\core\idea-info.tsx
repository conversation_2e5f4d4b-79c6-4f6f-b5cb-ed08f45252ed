"use client";
import moment from "moment";
import { toast } from "sonner";
import IdeaStatus from "./status";
import { Building2, Shield, ShieldCheck } from "lucide-react";
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  Too<PERSON><PERSON><PERSON>rovider,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";
import { useConfirm } from "@workspace/ui/components/confirm-dialog";
import { Button } from "@workspace/ui/components/button";
import UpdateIdea from "./edit-idea";
import { InlineEditField } from "@workspace/ui/components/inline-field";
import LoadingSpinner from "@workspace/ui/components/loading-spinner";
import { useQuery, useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { useRouter } from "next/navigation";

const IdeaInfo = ({ id }: { id: string }) => {
  const router = useRouter();

  // Fetch idea using Convex
  const idea = useQuery(api.ideas.getIdea, { id: id as Id<"Idea"> });
  const isPending = idea === undefined;

  // Generic mutation for updating idea fields
  const updateFieldMutation = useMutation(api.ideas.updateIdea);

  const handleUpdateField = async (field: string, value: string | boolean) => {
    try {
      const updateData: any = {};
      updateData[field] = value;

      await updateFieldMutation({
        id: id as Id<"Idea">,
        ...updateData,
      });

      toast.success(`${field} updated successfully`);
      router.refresh();
    } catch (error) {
      console.error("Error updating field:", error);
      toast.error(`Failed to update ${field}`);
    }
  };

  const confirm = useConfirm();

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: async () => {
      return await api.ideas.deleteIdea({ id: id as any });
    },
    onSuccess: () => {
      toast.success("Idea deleted successfully");
      // Invalidate ideas list and redirect
      queryClient.invalidateQueries({ queryKey: ["ideas"] });
      router.push("/ideas");
    },
    onError: (error) => {
      console.error("Error deleting idea:", error);
      toast.error("Failed to delete idea. Please try again.");
    },
  });

  const handleDelete = async () => {
    const isConfirmed = await confirm({
      title: "Delete idea?",
      description:
        "This action will permanently remove your idea and everything related to it.",
    });

    if (isConfirmed) {
      deleteMutation.mutate();
    }
  };

  if (isPending) return <LoadingSpinner />;

  return (
    <div className="h-full flex flex-col p-6">
      <div className="flex justify-between w-full flex-wrap items-center gap-4">
        <div className="flex flex-wrap items-start justify-between w-full">
          <div>
            <InlineEditField
              value={`${idea?.name}` || ""}
              onSave={(value) => handleUpdateField("name", value)}
              className="text-2xl font-medium hover:bg-transparent focus:ring-2 focus:ring-offset-2 focus:ring-primary/20 rounded px-2 -ml-2"
              disabled={updateFieldMutation.isPending}
            />
            <div className="flex flex-wrap gap-6 py-2 text-sm text-muted-foreground">
              <div className="text-muted-foreground text-sm">
                Added {moment(idea?._creationTime).fromNow()}
              </div>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger className="flex items-center gap-2 hover:text-foreground transition-colors">
                    <Building2 className="h-4 w-4 text-indigo-500" />
                    {idea?.industry}
                  </TooltipTrigger>
                  <TooltipContent>Industry classification</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger className="flex items-center gap-2 hover:text-foreground transition-colors">
                    <Shield className="h-4 w-4 text-blue-500" />
                    {idea?.internal ? "Internal idea" : "Client/external idea"}
                  </TooltipTrigger>
                  <TooltipContent>Idea ownership type</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger className="flex items-center gap-2 hover:text-foreground transition-colors">
                    <ShieldCheck className="h-4 w-4 text-purple-500" />
                    {idea?.openSource ? "Open source" : "Closed source"}
                  </TooltipTrigger>
                  <TooltipContent>Source code accessibility</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {idea?.status && (
              <IdeaStatus refetch={() => {}} id={id} status={idea.status} />
            )}
            <div className="flex items-center gap-4">
              <UpdateIdea id={id} idea={idea} onSuccess={() => {}}>
                <Button variant="outline" className="w-full">
                  Edit Idea
                </Button>
              </UpdateIdea>
              <Button
                variant="destructive"
                className="w-full"
                size="sm"
                onClick={handleDelete}
                disabled={deleteMutation.isPending}
              >
                {deleteMutation.isPending ? "Deleting..." : "Delete"}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IdeaInfo;
