"use client";

import { useId, useState } from "react";
import { CheckIcon } from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@workspace/ui/components/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import { projectTypes } from "@/utils/constants/projects/projectTypes";
import { TbListDetails } from "react-icons/tb";
import { useQuery } from "convex/react";
import { api, Id } from "@workspace/backend";

interface ProjectSelectorProps {
  currentProject?: Id<"Project"> | null;
  onChange: (project: Id<"Project"> | null) => void;
}

export function ProjectSelector({
  currentProject,
  onChange,
}: ProjectSelectorProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);

  const handleProjectChange = (projectId: Id<"Project">) => {
    onChange(projectId);
    setOpen(false);
  };

  const projects = useQuery(api.projects.index.getAllProjects);

  const selectedProject = (projects ?? []).find(
    (project) => project._id === currentProject
  );

  // Find the project type configuration for the selected project
  const selectedProjectType = selectedProject
    ? projectTypes.find((type) => type.id === selectedProject.platform)
    : null;

  return (
    <div className="*:not-first:mt-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={id}
            className="flex items-center justify-center"
            size="sm"
            variant="secondary"
            role="combobox"
            aria-expanded={open}
            disabled={projects === undefined}
          >
            {projects === undefined ? (
              <div className="flex items-center gap-2">
                <div className="size-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                <span>Loading...</span>
              </div>
            ) : selectedProject ? (
              <div className="flex items-center gap-2">
                {selectedProjectType ? (
                  <selectedProjectType.icon
                    className={selectedProjectType.colorClass + " size-4"}
                  />
                ) : (
                  <TbListDetails className="text-muted-foreground size-4" />
                )}
                <span>{selectedProject.name}</span>
              </div>
            ) : (
              <span>Select Project</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="border-input w-full min-w-[var(--radix-popper-anchor-width)] p-0"
          align="start"
        >
          <Command>
            <CommandInput placeholder="Search projects..." />
            <CommandList>
              <CommandEmpty>
                {projects === undefined ? (
                  <div className="flex items-center justify-center py-6">
                    <div className="size-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    <span className="ml-2">Loading projects...</span>
                  </div>
                ) : (
                  "No projects found."
                )}
              </CommandEmpty>
              <CommandGroup>
                {(projects ?? []).map((project) => {
                  const projectType = projectTypes.find(
                    (type) => type.id === project.platform
                  );
                  return (
                    <CommandItem
                      key={project._id}
                      value={project._id}
                      onSelect={() => handleProjectChange(project._id)}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-2">
                        {projectType ? (
                          <projectType.icon
                            className={projectType.colorClass + " size-4"}
                          />
                        ) : (
                          <TbListDetails className="text-muted-foreground size-4" />
                        )}
                        <span>{project.name}</span>
                      </div>
                      {currentProject === project._id && (
                        <CheckIcon size={16} className="ml-auto" />
                      )}
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
