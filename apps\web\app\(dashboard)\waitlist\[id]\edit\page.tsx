"use client";

import React from "react";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import Header from "@/components/shared/header";
import WaitlistForm from "../../_components/waitlist-form";
import { Loader2 } from "lucide-react";

interface EditWaitlistPageProps {
  params: Promise<{ id: string }>;
}

export default function EditWaitlistPage({
  params,
}: EditWaitlistPageProps) {
  const { id: waitlistId } = React.use(params);

  // Use Convex hooks for real-time data
  const waitlist = useQuery(api.projects.waitlist.getWaitlist, {
    id: waitlistId as Id<"Waitlist">,
  });

  const isLoading = waitlist === undefined;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin" />
      </div>
    );
  }

  if (!waitlist) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <h2 className="text-lg font-semibold">Waitlist not found</h2>
          <p className="text-muted-foreground">
            The waitlist you're looking for doesn't exist or you don't have access to it.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <Header
        crumb={[
          { title: "Waitlists", url: "/waitlist" },
          { title: waitlist.name, url: `/waitlist/${waitlistId}` },
          { title: "Edit" },
        ]}
      >
        <></>
      </Header>
      <WaitlistForm
        mode="edit"
        waitlistId={waitlistId}
        initialData={{
          name: waitlist.name,
          slug: waitlist.slug,
          description: waitlist.description,
          projectId: waitlist.projectId,
          projectName: waitlist.project?.name,
          isPublic: waitlist.isPublic,
          allowNameCapture: waitlist.allowNameCapture,
          showPosition: waitlist.showPosition,
          showSocialProof: waitlist.showSocialProof,
          customMessage: waitlist.customMessage || "",
          emailSyncEnabled: waitlist.emailSyncEnabled || false,
          integrationId: waitlist.integrationId || null,
        }}
      />
    </div>
  );
}
