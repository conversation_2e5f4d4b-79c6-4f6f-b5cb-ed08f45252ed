import { httpRouter } from "convex/server";
import { betterAuth } from "./auth";
import { createAuth } from "../lib/auth";
import { httpAction } from "./_generated/server";
import { internal } from "./_generated/api";

const http = httpRouter();

betterAuth.registerRoutes(http, createAuth);

// HTTP endpoint for syncing subscriptions
http.route({
  path: "/sync-subscription",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    const body = await request.json();

    // Validate the request body
    const { org, productId, userId, status, subscription_id } = body;

    if (!org || !productId || !userId || !status || !subscription_id) {
      return new Response(
        JSON.stringify({ error: "Missing required fields" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    try {
      // Call the internal mutation to sync the subscription
      await ctx.runMutation(internal.subscription.performSubscriptionSync, {
        org,
        productId,
        userId,
        status,
        subscription_id,
      });

      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { "Content-Type": "application/json" },
      });
    } catch (error) {
      console.error("Error syncing subscription:", error);
      return new Response(
        JSON.stringify({ error: "Failed to sync subscription" }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  }),
});

export default http;
