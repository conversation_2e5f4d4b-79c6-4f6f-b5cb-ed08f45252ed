"use client";

import { useMutation, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";

import { Id } from "@workspace/backend/convex/_generated/dataModel";

import { Button } from "@workspace/ui/components/button";
import { Separator } from "@workspace/ui/components/separator";
import { Database, Lock, Trash, Bot, Server } from "lucide-react";
import { useRouter } from "next/navigation";
import { formatDistanceToNow } from "date-fns";
import { CommandSelect } from "@workspace/ui/components/command-select";
import { toast } from "sonner";
import { DATABASE_PROVIDERS } from "@/utils/constants/sources/database";
import { AUTH_PROVIDERS } from "@/utils/constants/sources/auth";
import { ProjectTypeSelector } from "@/components/ui/selectors/project-type-selector";
import { ImConnection } from "react-icons/im";
import { useConfirm } from "@workspace/ui/components/confirm-dialog";
import { DateSelector } from "@/components/ui/selectors";
import { INFRASTRUCTURE_PROVIDERS } from "@/utils/constants/sources/infrastructure";
import { ProjectStatusSelector } from "@/components/ui/selectors/project-status-selector";
import { ActivityFeed } from "@/components/shared";
import { ORM_PLATFORMS } from "@/utils/constants/sources/orms";
import { AI_PLATFORMS } from "@/utils/constants/sources/ai";

interface ProjectSidebarProps {
  projectId: string;
}

export function ProjectSidebar({ projectId }: ProjectSidebarProps) {
  const router = useRouter();
  const confirm = useConfirm();

  // Fetch project using Convex

  const project = useQuery(api.projects.index.getProjectById, {
    id: projectId as Id<"Project">,
  });

  // Update project mutation using Convex
  const updateProjectMutation = useMutation(api.projects.index.updateProject);

  // Delete project mutation using Convex
  const deleteProjectMutation = useMutation(api.projects.index.deleteProject);


  const handleDeleteProject = async () => {
    try {
      const isConfirmed = await confirm({
        description: "Are you sure you want to delete this project?",
        title: "Delete Project",
      });
      if (isConfirmed) {

        await deleteProjectMutation({
          id: projectId as Id<"Project">,
        });

        toast.success("Project deleted successfully");
        router.push("/project");
      }
    } catch (error) {
      toast.error("Failed to delete project");
    }
  };

  if (!project) {
    return (
      <div className="p-4">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-muted rounded w-1/2"></div>
          <div className="space-y-2">
            <div className="h-4 bg-muted rounded"></div>
            <div className="h-4 bg-muted rounded w-3/4"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="flex justify-between items-center gap-4 mb-6">
        <h1 className="text-lg font-medium">Properties</h1>
        <Button size="icon" variant="destructive" onClick={handleDeleteProject}>
          <Trash />
        </Button>
      </div>
      <div className="space-y-3">
        <h3 className="font-medium text-muted-foreground mb-6">Project Info</h3>
        <div className="grid grid-cols-[120px_1fr] gap-y-4">
          <h3 className="text-xs font-medium text-muted-foreground">Status</h3>
          <ProjectStatusSelector

            onChange={async (value) => {
              try {
                await updateProjectMutation({
                  id: project._id,
                  status: value,
                });
                toast.success("Project status updated");
              } catch (error) {
                toast.error("Failed to update project status");
              }
            }}

            status={project.status}
          />

          <h3 className="text-xs font-medium text-muted-foreground">
            Platform
          </h3>
          <ProjectTypeSelector
            selectedType={project.platform}

            onChange={async (value) => {
              try {
                await updateProjectMutation({
                  id: project._id,
                  platform: value,
                });
                toast.success("Project platform updated");
              } catch (error) {
                toast.error("Failed to update project platform");
              }
            }}

          />

          <h3 className="text-xs font-medium text-muted-foreground">
            Due date
          </h3>
          <DateSelector
            value={project?.dueDate ? new Date(project?.dueDate) : null}

            onChange={async (e) => {
              try {
                await updateProjectMutation({
                  id: project._id,
                  dueDate: e?.getTime(),
                });
                toast.success("Project due date updated");
              } catch (error) {
                toast.error("Failed to update project due date");
              }
            }}

          />

          <h3 className="text-xs font-medium text-muted-foreground">Added</h3>
          <span className="text-sm">
            {formatDistanceToNow(new Date(project.createdAt), {
              addSuffix: true,
            })}
          </span>
          <h3 className="text-xs font-medium text-muted-foreground">
            Last Updated
          </h3>
          <span className="text-sm">
            {formatDistanceToNow(new Date(project.updatedAt), {
              addSuffix: true,
            })}
          </span>
        </div>
      </div>
      <Separator className="my-3" />
      <div className="space-y-3">
        <h3 className="font-medium text-muted-foreground mb-6">Tech Stack</h3>
        <div className="grid grid-cols-[120px_1fr] gap-y-4">
          <h3 className="text-xs font-medium text-muted-foreground">ORM</h3>
          <CommandSelect
            options={ORM_PLATFORMS.map((option) => ({
              value: option.name,
              label: option.name,
              icon: <ImConnection />,
            }))}

            onValueChange={async (value) => {
              try {
                await updateProjectMutation({
                  id: project._id,
                  orm: value,
                });
                toast.success("Project ORM updated");
              } catch (error) {
                toast.error("Failed to update project ORM");
              }
            }}

            value={project?.orm || ""}
            placeholder="Select ORM"
          />

          <h3 className="text-xs font-medium text-muted-foreground">
            Database
          </h3>
          <CommandSelect
            options={DATABASE_PROVIDERS.map((option) => ({
              value: option.name,
              label: option.name,
              icon: <Database />,
            }))}

            onValueChange={async (value) => {
              try {
                await updateProjectMutation({
                  id: project._id,
                  database: value,
                });
                toast.success("Project database updated");
              } catch (error) {
                toast.error("Failed to update project database");
              }
            }}

            value={project?.database || ""}
            placeholder="Select Database"
          />

          <h3 className="text-xs font-medium text-muted-foreground">Auth</h3>
          <CommandSelect
            options={AUTH_PROVIDERS.map((option) => ({
              value: option.name,
              label: option.name,
              icon: <Lock />,
            }))}

            onValueChange={async (value) => {
              try {
                await updateProjectMutation({
                  id: project._id,
                  auth: value,
                });
                toast.success("Project auth updated");
              } catch (error) {
                toast.error("Failed to update project auth");
              }
            }}

            value={project.auth || ""}
            placeholder="Select Auth provider"
          />
          <h3 className="text-xs font-medium text-muted-foreground">AI</h3>
          <CommandSelect
            options={AI_PLATFORMS.map((option) => ({
              value: option.name,
              label: option.name,
              icon: <Bot />,
            }))}

            onValueChange={async (value) => {
              try {
                await updateProjectMutation({
                  id: project._id,
                  ai: value,
                });
                toast.success("Project AI updated");
              } catch (error) {
                toast.error("Failed to update project AI");
              }
            }}

            value={project?.ai || ""}
            placeholder="Select AI provider"
          />

          <h3 className="text-xs font-medium text-muted-foreground">
            Infrastructure
          </h3>
          <CommandSelect
            options={INFRASTRUCTURE_PROVIDERS.map((option) => ({
              value: option.name,
              label: option.name,
              icon: <Server />,
            }))}

            onValueChange={async (value) => {
              try {
                await updateProjectMutation({
                  id: project._id,
                  infrastructure: value,
                });
                toast.success("Project infrastructure updated");
              } catch (error) {
                toast.error("Failed to update project infrastructure");
              }
            }}

            value={project?.infrastructure || ""}
            placeholder="Select infrastructure"
          />
        </div>
      </div>
      {/* <ActivityFeed
        entityId={project?.id}
        entityType="PROJECT"
        emptyMessage="No activity yet"
        limit={20}
      /> */}
    </div>
  );
}
